'use client';

import React, { useState, useEffect } from 'react';
import CharacterCreation from '../src/components/CharacterCreation';
import GameScreen from '../src/components/GameScreen';
import AISceneGenerator from '../src/components/AISceneGenerator';
import SaveLoadModal from '../src/components/SaveLoadModal';
import { Character, Scene, Choice, Decision, Campaign, SaveData } from '../src/types/game';
import { useGameStore, useProgressStore } from '../src/store/gameStore';
import { generateId } from '../src/utils/gameUtils';
import { FirebaseService } from '../src/services/firebaseService';

// Sample campaign for testing
const sampleCampaign: Campaign = {
  id: 'sample-campaign',
  title: 'The Mysterious Forest',
  setting: 'A dark and enchanted forest',
  theme: 'Mystery and Adventure',
  keyNpcs: ['The Forest Guardian', 'A Lost Merchant'],
  synopsis: 'You find yourself at the edge of a mysterious forest, drawn by rumors of ancient treasures hidden within its depths.',
  acts: [],
  isUnlocked: true
};

// Sample scene for testing
const sampleScene: Scene = {
  id: 'forest-entrance',
  title: 'The Forest Entrance',
  description: `You stand before the entrance to the mysterious forest. Ancient trees tower above you, their branches intertwining to form a natural canopy that blocks out most of the sunlight. A narrow path winds into the darkness ahead.

You can hear strange sounds echoing from within - the rustle of leaves, distant animal calls, and something else... something that makes your skin crawl.

What do you choose to do?`,
  choices: [
    {
      id: 'enter-boldly',
      text: 'Enter the forest boldly, weapon drawn',
      consequence: 'You stride confidently into the forest, ready for whatever awaits.',
      requiresCheck: {
        stat: 'might',
        difficulty: 12,
        successText: 'Your bold approach intimidates a pack of wolves, and they retreat without a fight.',
        failureText: 'Your loud entrance attracts unwanted attention from forest predators.'
      }
    },
    {
      id: 'sneak-carefully',
      text: 'Sneak carefully along the path, staying alert',
      consequence: 'You move stealthily through the underbrush, avoiding potential dangers.',
      requiresCheck: {
        stat: 'wit',
        difficulty: 14,
        successText: 'You notice hidden traps along the path and avoid them skillfully.',
        failureText: 'Despite your caution, you step on a twig that alerts nearby creatures.'
      }
    },
    {
      id: 'call-out',
      text: 'Call out to see if anyone is there',
      consequence: 'Your voice echoes through the forest, potentially attracting attention.',
      requiresCheck: {
        stat: 'charm',
        difficulty: 13,
        successText: 'A friendly forest sprite appears and offers to guide you safely.',
        failureText: 'Your call attracts hostile creatures who see you as an intruder.'
      }
    }
  ],
  isGenerated: false
};

export default function Home() {
  const [gameState, setGameState] = useState<'menu' | 'character-creation' | 'playing'>('menu');
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null);
  const [currentScene, setCurrentScene] = useState<Scene>(sampleScene);
  const [showAIGenerator, setShowAIGenerator] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [isFirebaseReady, setIsFirebaseReady] = useState(false);

  const { startNewGame, makeDecision, nextScene, loadGame } = useGameStore();
  const { unlockedClasses } = useProgressStore();

  // Initialize Firebase on component mount
  useEffect(() => {
    const initFirebase = async () => {
      try {
        // Check if Firebase is available first
        if (!FirebaseService.isAvailable()) {
          console.log('Firebase not configured, running in local mode');
          setIsFirebaseReady(false);
          return;
        }

        await FirebaseService.initializeUser();
        setIsFirebaseReady(true);
        console.log('Firebase initialized successfully');
      } catch (error) {
        console.error('Firebase initialization failed:', error);
        // Continue without Firebase (local mode)
        setIsFirebaseReady(false);
      }
    };

    initFirebase();
  }, []);

  const handleCharacterCreated = (character: Character) => {
    setCurrentCharacter(character);
    startNewGame(character, sampleCampaign);
    setGameState('playing');
  };

  const handleChoiceMade = (choice: Choice, decision: Decision) => {
    makeDecision(decision);

    console.log('Choice made:', choice.text);
    console.log('Decision recorded:', decision);

    // Generate a follow-up scene based on the choice made
    generateFollowUpScene(choice, decision);
  };

  const generateFollowUpScene = (choice: Choice, decision: Decision) => {
    // Create a follow-up scene based on the choice outcome
    const followUpScene: Scene = {
      id: `scene-${Date.now()}`,
      title: getFollowUpTitle(choice),
      description: getFollowUpDescription(choice, decision),
      choices: getFollowUpChoices(choice),
      isGenerated: false
    };

    setCurrentScene(followUpScene);
  };

  const getFollowUpTitle = (choice: Choice): string => {
    switch (choice.id) {
      case 'enter-boldly':
        return 'Deep in the Forest';
      case 'sneak-carefully':
        return 'The Hidden Path';
      case 'call-out':
        return 'An Unexpected Encounter';
      default:
        return 'The Adventure Continues';
    }
  };

  const getFollowUpDescription = (choice: Choice, decision: Decision): string => {
    const baseOutcome = decision.outcome;

    switch (choice.id) {
      case 'enter-boldly':
        return `${baseOutcome}

As you venture deeper into the forest, the canopy above grows thicker, blocking out most of the remaining sunlight. Ancient trees tower around you like silent sentinels, their gnarled branches creating an intricate maze overhead.

You notice strange markings carved into some of the tree trunks - symbols that seem to pulse with a faint, otherworldly light. The air grows colder, and you can hear the distant sound of running water.

What do you do next?`;

      case 'sneak-carefully':
        return `${baseOutcome}

Your cautious approach has led you to a narrow, winding path that seems to be used by forest creatures rather than humans. Moss-covered stones mark the way, and you notice that the plants here seem more vibrant and alive than those at the forest's edge.

As you follow the path, you discover a small clearing where shafts of golden sunlight pierce through the canopy. In the center stands an ancient stone altar, covered in intricate carvings and surrounded by wildflowers that seem to glow with their own inner light.

How do you proceed?`;

      case 'call-out':
        return `${baseOutcome}

The forest seems to respond to your call in ways you didn't expect. The very air around you shimmers, and you feel a presence watching you from the shadows between the trees.

Suddenly, a figure emerges from behind a massive oak tree. They appear to be neither fully human nor entirely something else - tall and graceful, with eyes that reflect the forest's ancient wisdom. They regard you with curiosity rather than hostility.

"Few dare to announce themselves so boldly in these woods," they say in a voice like rustling leaves. "What brings you to the heart of the old forest?"

What is your response?`;

      default:
        return `${baseOutcome}

The forest path winds deeper into the unknown, and new challenges await you ahead.`;
    }
  };

  const getFollowUpChoices = (originalChoice: Choice): Choice[] => {
    switch (originalChoice.id) {
      case 'enter-boldly':
        return [
          {
            id: 'investigate-markings',
            text: 'Investigate the glowing markings on the trees',
            consequence: 'You approach the mysterious symbols.',
            requiresCheck: {
              stat: 'wit',
              difficulty: 15,
              successText: 'You decipher part of the ancient script and learn about a hidden treasure.',
              failureText: 'The symbols remain a mystery, but you sense they hold great power.'
            }
          },
          {
            id: 'follow-water-sound',
            text: 'Follow the sound of running water',
            consequence: 'You head toward the distant sound of water.',
            requiresCheck: {
              stat: 'might',
              difficulty: 12,
              successText: 'You push through thick undergrowth and find a crystal-clear stream.',
              failureText: 'The dense vegetation slows your progress, and you lose the sound.'
            }
          },
          {
            id: 'make-camp',
            text: 'Set up a defensive position and rest',
            consequence: 'You decide to rest and observe your surroundings.',
            requiresCheck: {
              stat: 'charm',
              difficulty: 13,
              successText: 'You find a perfect spot and feel the forest\'s protective presence.',
              failureText: 'You struggle to find a comfortable spot and remain on edge.'
            }
          }
        ];

      case 'sneak-carefully':
        return [
          {
            id: 'examine-altar',
            text: 'Carefully examine the ancient altar',
            consequence: 'You approach the mysterious stone altar.',
            requiresCheck: {
              stat: 'wit',
              difficulty: 14,
              successText: 'You discover the altar is a shrine to forest spirits and gain their blessing.',
              failureText: 'The altar\'s purpose remains unclear, but you sense its importance.'
            }
          },
          {
            id: 'gather-flowers',
            text: 'Collect some of the glowing wildflowers',
            consequence: 'You reach for the luminescent flowers.',
            requiresCheck: {
              stat: 'charm',
              difficulty: 13,
              successText: 'The flowers willingly come to you, and you feel their magical energy.',
              failureText: 'The flowers dim as you approach, sensing your uncertainty.'
            }
          },
          {
            id: 'circle-clearing',
            text: 'Circle the clearing to look for other paths',
            consequence: 'You scout the perimeter of the clearing.',
            requiresCheck: {
              stat: 'might',
              difficulty: 11,
              successText: 'You find a hidden trail leading to what appears to be ruins.',
              failureText: 'The undergrowth is too thick, and you return to the clearing.'
            }
          }
        ];

      case 'call-out':
        return [
          {
            id: 'ask-guidance',
            text: 'Ask the forest being for guidance on your quest',
            consequence: 'You seek wisdom from the mysterious guardian.',
            requiresCheck: {
              stat: 'charm',
              difficulty: 16,
              successText: 'The being smiles and offers to guide you to a place of power.',
              failureText: 'The being remains neutral, neither helping nor hindering you.'
            }
          },
          {
            id: 'offer-trade',
            text: 'Offer to trade something for safe passage',
            consequence: 'You attempt to negotiate with the forest guardian.',
            requiresCheck: {
              stat: 'wit',
              difficulty: 14,
              successText: 'You offer knowledge of the outside world, which intrigues them.',
              failureText: 'Your offer doesn\'t interest them, but they appreciate the gesture.'
            }
          },
          {
            id: 'show-respect',
            text: 'Bow respectfully and ask permission to pass',
            consequence: 'You show proper respect to the forest\'s guardian.',
            requiresCheck: {
              stat: 'charm',
              difficulty: 12,
              successText: 'Your respect is acknowledged, and you receive a blessing for your journey.',
              failureText: 'Your gesture is noted, but you must prove your worth another way.'
            }
          }
        ];

      default:
        return [
          {
            id: 'continue-forward',
            text: 'Continue forward on the path',
            consequence: 'You press onward into the unknown.',
            requiresCheck: {
              stat: 'might',
              difficulty: 12,
              successText: 'You make good progress through the forest.',
              failureText: 'The path becomes more difficult to follow.'
            }
          }
        ];
    }
  };

  const handleAISceneGenerated = (scene: Scene, synopsis: string) => {
    setCurrentScene(scene);
    // Add synopsis to game state
    console.log('AI Scene generated:', scene.title);
    console.log('Synopsis:', synopsis);
  };

  const handleSaveGame = (saveId: string) => {
    console.log('Game saved with ID:', saveId);
    // Could show a success message here
  };

  const handleLoadGame = (saveData: SaveData) => {
    loadGame(saveData);
    if (saveData.characterState) {
      setCurrentCharacter(saveData.characterState);
      setGameState('playing');
    }
    console.log('Game loaded:', saveData);
  };

  const handleStartGame = () => {
    setGameState('character-creation');
  };

  const handleBackToMenu = () => {
    setGameState('menu');
    setCurrentCharacter(null);
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {gameState === 'menu' && (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">
              TextQuest
            </h1>
            <p className="text-xl mb-8 text-gray-300">
              A text-based adventure with D&D elements and AI-generated stories
            </p>
            <div className="space-y-4">
              <button
                onClick={handleStartGame}
                className="block mx-auto px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold text-lg transition-colors"
              >
                Start New Game
              </button>
              <button
                onClick={() => setShowLoadModal(true)}
                className="block mx-auto px-8 py-4 bg-green-600 hover:bg-green-700 rounded-lg font-semibold text-lg transition-colors"
                disabled={!isFirebaseReady}
              >
                Load Game {!isFirebaseReady && '(Firebase Required)'}
              </button>
              <button
                className="block mx-auto px-8 py-4 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold text-lg transition-colors"
                disabled
              >
                Multiplayer (Coming Soon)
              </button>

              {/* API Status */}
              <div className="mt-6 text-center text-sm">
                <div className="space-y-1">
                  <div className={`flex items-center justify-center gap-2 ${
                    process.env.NEXT_PUBLIC_OPENAI_API_KEY ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    <span>{process.env.NEXT_PUBLIC_OPENAI_API_KEY ? '✅' : '⚠️'}</span>
                    <span>AI Generation: {process.env.NEXT_PUBLIC_OPENAI_API_KEY ? 'Ready' : 'Local Mode'}</span>
                  </div>
                  <div className={`flex items-center justify-center gap-2 ${
                    isFirebaseReady ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    <span>{isFirebaseReady ? '✅' : '⚠️'}</span>
                    <span>Cloud Saves: {isFirebaseReady ? 'Ready' : 'Local Mode'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {gameState === 'character-creation' && (
        <div className="py-8">
          <div className="mb-4 text-center">
            <button
              onClick={handleBackToMenu}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-white transition-colors"
            >
              ← Back to Menu
            </button>
          </div>
          <CharacterCreation
            onCharacterCreated={handleCharacterCreated}
            unlockedClasses={unlockedClasses}
          />
        </div>
      )}

      {gameState === 'playing' && currentCharacter && (
        <div>
          <div className="p-4 bg-gray-800 flex justify-between items-center">
            <button
              onClick={handleBackToMenu}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-white transition-colors"
            >
              ← Back to Menu
            </button>

            <div className="flex gap-2">
              <button
                onClick={() => setShowAIGenerator(!showAIGenerator)}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-white transition-colors"
                disabled={!process.env.NEXT_PUBLIC_OPENAI_API_KEY}
              >
                {showAIGenerator ? 'Hide' : 'Show'} AI Generator
              </button>
              <button
                onClick={() => setShowSaveModal(true)}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white transition-colors"
                disabled={!isFirebaseReady}
              >
                Save Game
              </button>
              <button
                onClick={() => setShowLoadModal(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white transition-colors"
                disabled={!isFirebaseReady}
              >
                Load Game
              </button>
            </div>
          </div>

          {/* AI Scene Generator */}
          {showAIGenerator && process.env.NEXT_PUBLIC_OPENAI_API_KEY && (
            <div className="p-4 bg-gray-900">
              <AISceneGenerator
                character={currentCharacter}
                campaign={sampleCampaign}
                currentAct={1}
                currentScene={1}
                onSceneGenerated={handleAISceneGenerated}
                onError={(error) => console.error('AI Generation Error:', error)}
              />
            </div>
          )}

          <GameScreen
            character={currentCharacter}
            currentScene={currentScene}
            onChoiceMade={handleChoiceMade}
          />
        </div>
      )}

      {/* Modals */}
      <SaveLoadModal
        isOpen={showSaveModal}
        mode="save"
        onClose={() => setShowSaveModal(false)}
        onSave={handleSaveGame}
      />

      <SaveLoadModal
        isOpen={showLoadModal}
        mode="load"
        onClose={() => setShowLoadModal(false)}
        onLoad={handleLoadGame}
      />
    </div>
  );
}
