'use client';

import React, { useState, useEffect } from 'react';
import CharacterCreation from '../src/components/CharacterCreation';
import GameScreen from '../src/components/GameScreen';
import AISceneGenerator from '../src/components/AISceneGenerator';
import SaveLoadModal from '../src/components/SaveLoadModal';
import { Character, Scene, Choice, Decision, Campaign, SaveData } from '../src/types/game';
import { useGameStore, useProgressStore } from '../src/store/gameStore';
import { generateId } from '../src/utils/gameUtils';
import { FirebaseService } from '../src/services/firebaseService';

// Sample campaign for testing
const sampleCampaign: Campaign = {
  id: 'sample-campaign',
  title: 'The Mysterious Forest',
  setting: 'A dark and enchanted forest',
  theme: 'Mystery and Adventure',
  keyNpcs: ['The Forest Guardian', 'A Lost Merchant'],
  synopsis: 'You find yourself at the edge of a mysterious forest, drawn by rumors of ancient treasures hidden within its depths.',
  acts: [],
  isUnlocked: true
};

// Sample scene for testing
const sampleScene: Scene = {
  id: 'forest-entrance',
  title: 'The Forest Entrance',
  description: `You stand before the entrance to the mysterious forest. Ancient trees tower above you, their branches intertwining to form a natural canopy that blocks out most of the sunlight. A narrow path winds into the darkness ahead.

You can hear strange sounds echoing from within - the rustle of leaves, distant animal calls, and something else... something that makes your skin crawl.

What do you choose to do?`,
  choices: [
    {
      id: 'enter-boldly',
      text: 'Enter the forest boldly, weapon drawn',
      consequence: 'You stride confidently into the forest, ready for whatever awaits.',
      requiresCheck: {
        stat: 'might',
        difficulty: 12,
        successText: 'Your bold approach intimidates a pack of wolves, and they retreat without a fight.',
        failureText: 'Your loud entrance attracts unwanted attention from forest predators.'
      }
    },
    {
      id: 'sneak-carefully',
      text: 'Sneak carefully along the path, staying alert',
      consequence: 'You move stealthily through the underbrush, avoiding potential dangers.',
      requiresCheck: {
        stat: 'wit',
        difficulty: 14,
        successText: 'You notice hidden traps along the path and avoid them skillfully.',
        failureText: 'Despite your caution, you step on a twig that alerts nearby creatures.'
      }
    },
    {
      id: 'call-out',
      text: 'Call out to see if anyone is there',
      consequence: 'Your voice echoes through the forest, potentially attracting attention.',
      requiresCheck: {
        stat: 'charm',
        difficulty: 13,
        successText: 'A friendly forest sprite appears and offers to guide you safely.',
        failureText: 'Your call attracts hostile creatures who see you as an intruder.'
      }
    }
  ],
  isGenerated: false
};

export default function Home() {
  const [gameState, setGameState] = useState<'menu' | 'character-creation' | 'playing'>('menu');
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null);
  const [currentScene, setCurrentScene] = useState<Scene>(sampleScene);
  const [showAIGenerator, setShowAIGenerator] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [isFirebaseReady, setIsFirebaseReady] = useState(false);

  const { startNewGame, makeDecision, nextScene, loadGame } = useGameStore();
  const { unlockedClasses } = useProgressStore();

  // Initialize Firebase on component mount
  useEffect(() => {
    const initFirebase = async () => {
      try {
        // Check if Firebase is available first
        if (!FirebaseService.isAvailable()) {
          console.log('Firebase not configured, running in local mode');
          setIsFirebaseReady(false);
          return;
        }

        await FirebaseService.initializeUser();
        setIsFirebaseReady(true);
        console.log('Firebase initialized successfully');
      } catch (error) {
        console.error('Firebase initialization failed:', error);
        // Continue without Firebase (local mode)
        setIsFirebaseReady(false);
      }
    };

    initFirebase();
  }, []);

  const handleCharacterCreated = (character: Character) => {
    setCurrentCharacter(character);
    startNewGame(character, sampleCampaign);
    setGameState('playing');
  };

  const handleChoiceMade = (choice: Choice, decision: Decision) => {
    makeDecision(decision);

    // For now, just show the consequence and stay on the same scene
    // In a full implementation, this would navigate to the next scene
    console.log('Choice made:', choice.text);
    console.log('Decision recorded:', decision);

    // TODO: Navigate to next scene or generate new scene
  };

  const handleAISceneGenerated = (scene: Scene, synopsis: string) => {
    setCurrentScene(scene);
    // Add synopsis to game state
    console.log('AI Scene generated:', scene.title);
    console.log('Synopsis:', synopsis);
  };

  const handleSaveGame = (saveId: string) => {
    console.log('Game saved with ID:', saveId);
    // Could show a success message here
  };

  const handleLoadGame = (saveData: SaveData) => {
    loadGame(saveData);
    if (saveData.characterState) {
      setCurrentCharacter(saveData.characterState);
      setGameState('playing');
    }
    console.log('Game loaded:', saveData);
  };

  const handleStartGame = () => {
    setGameState('character-creation');
  };

  const handleBackToMenu = () => {
    setGameState('menu');
    setCurrentCharacter(null);
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {gameState === 'menu' && (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">
              TextQuest
            </h1>
            <p className="text-xl mb-8 text-gray-300">
              A text-based adventure with D&D elements and AI-generated stories
            </p>
            <div className="space-y-4">
              <button
                onClick={handleStartGame}
                className="block mx-auto px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold text-lg transition-colors"
              >
                Start New Game
              </button>
              <button
                onClick={() => setShowLoadModal(true)}
                className="block mx-auto px-8 py-4 bg-green-600 hover:bg-green-700 rounded-lg font-semibold text-lg transition-colors"
                disabled={!isFirebaseReady}
              >
                Load Game {!isFirebaseReady && '(Firebase Required)'}
              </button>
              <button
                className="block mx-auto px-8 py-4 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold text-lg transition-colors"
                disabled
              >
                Multiplayer (Coming Soon)
              </button>

              {/* API Status */}
              <div className="mt-6 text-center text-sm">
                <div className="space-y-1">
                  <div className={`flex items-center justify-center gap-2 ${
                    process.env.NEXT_PUBLIC_OPENAI_API_KEY ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    <span>{process.env.NEXT_PUBLIC_OPENAI_API_KEY ? '✅' : '⚠️'}</span>
                    <span>AI Generation: {process.env.NEXT_PUBLIC_OPENAI_API_KEY ? 'Ready' : 'Local Mode'}</span>
                  </div>
                  <div className={`flex items-center justify-center gap-2 ${
                    isFirebaseReady ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    <span>{isFirebaseReady ? '✅' : '⚠️'}</span>
                    <span>Cloud Saves: {isFirebaseReady ? 'Ready' : 'Local Mode'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {gameState === 'character-creation' && (
        <div className="py-8">
          <div className="mb-4 text-center">
            <button
              onClick={handleBackToMenu}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-white transition-colors"
            >
              ← Back to Menu
            </button>
          </div>
          <CharacterCreation
            onCharacterCreated={handleCharacterCreated}
            unlockedClasses={unlockedClasses}
          />
        </div>
      )}

      {gameState === 'playing' && currentCharacter && (
        <div>
          <div className="p-4 bg-gray-800 flex justify-between items-center">
            <button
              onClick={handleBackToMenu}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-white transition-colors"
            >
              ← Back to Menu
            </button>

            <div className="flex gap-2">
              <button
                onClick={() => setShowAIGenerator(!showAIGenerator)}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-white transition-colors"
                disabled={!process.env.NEXT_PUBLIC_OPENAI_API_KEY}
              >
                {showAIGenerator ? 'Hide' : 'Show'} AI Generator
              </button>
              <button
                onClick={() => setShowSaveModal(true)}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white transition-colors"
                disabled={!isFirebaseReady}
              >
                Save Game
              </button>
              <button
                onClick={() => setShowLoadModal(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white transition-colors"
                disabled={!isFirebaseReady}
              >
                Load Game
              </button>
            </div>
          </div>

          {/* AI Scene Generator */}
          {showAIGenerator && process.env.NEXT_PUBLIC_OPENAI_API_KEY && (
            <div className="p-4 bg-gray-900">
              <AISceneGenerator
                character={currentCharacter}
                campaign={sampleCampaign}
                currentAct={1}
                currentScene={1}
                onSceneGenerated={handleAISceneGenerated}
                onError={(error) => console.error('AI Generation Error:', error)}
              />
            </div>
          )}

          <GameScreen
            character={currentCharacter}
            currentScene={currentScene}
            onChoiceMade={handleChoiceMade}
          />
        </div>
      )}

      {/* Modals */}
      <SaveLoadModal
        isOpen={showSaveModal}
        mode="save"
        onClose={() => setShowSaveModal(false)}
        onSave={handleSaveGame}
      />

      <SaveLoadModal
        isOpen={showLoadModal}
        mode="load"
        onClose={() => setShowLoadModal(false)}
        onLoad={handleLoadGame}
      />
    </div>
  );
}
