// Core game types for TextQuest

export interface GameStats {
  might: number;
  wit: number;
  charm: number;
}

export interface CharacterClass {
  name: string;
  stats: GameStats;
  abilities: string[];
}

export interface Character {
  id: string;
  name: string;
  class: CharacterClass;
  level: number;
  currentStats: GameStats;
  abilities: string[];
  experience: number;
  health: number;
  maxHealth: number;
}

export interface ShopItem {
  name: string;
  effect: string;
  cost: number;
  id: string;
}

export interface InventoryItem {
  item: ShopItem;
  quantity: number;
}

export interface GameCurrency {
  gold: number;
  tokens: number;
}

export interface SaveData {
  campaignId: string;
  currentAct: number;
  currentScene: number;
  characterState: Character;
  synopsisHistory: string[];
  inventory: InventoryItem[];
  decisionsMade: Decision[];
  currency: GameCurrency;
  timestamp: number;
}

export interface Decision {
  sceneId: string;
  choice: string;
  outcome: string;
  timestamp: number;
}

export interface Scene {
  id: string;
  title: string;
  description: string;
  choices: Choice[];
  requiredCheck?: AbilityCheck;
  isGenerated: boolean;
}

export interface Choice {
  id: string;
  text: string;
  consequence: string;
  nextSceneId?: string;
  requiresCheck?: AbilityCheck;
}

export interface AbilityCheck {
  stat: keyof GameStats;
  difficulty: number;
  successText: string;
  failureText: string;
}

export interface Act {
  id: string;
  title: string;
  scenes: Scene[];
  maxScenes: number;
}

export interface Campaign {
  id: string;
  title: string;
  setting: string;
  theme: string;
  keyNpcs: string[];
  acts: Act[];
  synopsis: string;
  isUnlocked: boolean;
}

export interface StoryArc {
  id: string;
  title: string;
  setting: string;
  theme: string;
  keyNpcs: string[];
  milestones: string[];
  endings: CampaignEnding[];
  maxScenesPerAct: number;
}

export type CampaignEnding = 'good' | 'neutral' | 'bad';

export interface GameMode {
  type: 'solo' | 'multiplayer';
  actsPerCampaign?: number;
  maxScenesPerAct?: number;
  hasShopBetweenActs?: boolean;
  playersRequired?: number;
  maxPlayers?: number;
  uniqueClassRequirement?: boolean;
  format?: string;
  endings?: CampaignEnding[];
  saveSystem: {
    enabled: boolean;
    fields?: string[];
  };
}

export interface MetaUnlock {
  type: 'class' | 'campaign' | 'starterItem';
  name: string;
  unlockRequirement: string;
  isUnlocked: boolean;
}

export interface PlayerProgress {
  completedCampaigns: string[];
  unlockedClasses: string[];
  unlockedCampaigns: string[];
  unlockedItems: string[];
  currency: GameCurrency;
  achievements: string[];
}

export interface GameState {
  currentMode: GameMode;
  currentCampaign?: Campaign;
  currentAct: number;
  currentScene: number;
  players: Character[];
  inventory: InventoryItem[];
  currency: GameCurrency;
  decisionHistory: Decision[];
  synopsisHistory: string[];
  isGameActive: boolean;
  lastSaved?: number;
}

export interface AIPromptContext {
  playerClass: string;
  currentStats: GameStats;
  inventory: InventoryItem[];
  currentAct: number;
  campaignSynopsis: string;
  recentDecisions: Decision[];
}

export interface AIGeneratedScene {
  scene: Scene;
  context: AIPromptContext;
  generatedAt: number;
}
