import { CharacterClass, ShopItem, MetaUnlock, GameMode } from '../types/game';

export const CHARACTER_CLASSES: CharacterClass[] = [
  {
    name: "<PERSON>",
    stats: { might: 4, wit: 1, charm: 2 },
    abilities: ["Shield Bash", "Battle Cry"]
  },
  {
    name: "Mage",
    stats: { might: 1, wit: 4, charm: 2 },
    abilities: ["Firebolt", "Arcane Insight"]
  },
  {
    name: "Rogue",
    stats: { might: 2, wit: 3, charm: 3 },
    abilities: ["Backstab", "Smoke Step"]
  },
  {
    name: "Healer",
    stats: { might: 1, wit: 3, charm: 4 },
    abilities: ["Heal", "Divine Barrier"]
  }
];

export const SHOP_ITEMS: ShopItem[] = [
  {
    id: "health-potion",
    name: "Health Potion",
    effect: "Restore 50% HP",
    cost: 10
  },
  {
    id: "revive-stone",
    name: "Revive Stone",
    effect: "Auto-revive once per act",
    cost: 25
  },
  {
    id: "damage-boost",
    name: "Damage Boost",
    effect: "+10% damage for next encounter",
    cost: 15
  },
  {
    id: "skill-scroll",
    name: "Skill Scroll",
    effect: "Extra use of one class ability",
    cost: 20
  },
  {
    id: "luck-charm",
    name: "Luck Charm",
    effect: "+1 to next ability check",
    cost: 10
  }
];

export const META_UNLOCKS: MetaUnlock[] = [
  {
    type: "class",
    name: "Shadowmancer",
    unlockRequirement: "Complete 3 horror-themed arcs",
    isUnlocked: false
  },
  {
    type: "campaign",
    name: "The Godwell Depths",
    unlockRequirement: "Reach Level 3 with any class",
    isUnlocked: false
  },
  {
    type: "starterItem",
    name: "Lucky Charm",
    unlockRequirement: "Earn 2 good endings",
    isUnlocked: false
  }
];

export const GAME_MODES: Record<string, GameMode> = {
  solo: {
    type: 'solo',
    actsPerCampaign: 3,
    maxScenesPerAct: 4,
    hasShopBetweenActs: true,
    saveSystem: {
      enabled: true,
      fields: [
        "campaignId",
        "currentAct",
        "currentScene",
        "characterState",
        "synopsisHistory",
        "inventory",
        "decisionsMade"
      ]
    }
  },
  multiplayer: {
    type: 'multiplayer',
    playersRequired: 2,
    maxPlayers: 4,
    uniqueClassRequirement: true,
    format: "oneShot",
    endings: ["good", "neutral", "bad"],
    saveSystem: {
      enabled: false
    }
  }
};

export const PROGRESSION_CONFIG = {
  type: "soft",
  maxLevel: 5,
  statGainPerLevel: 1,
  maxStatValue: 6,
  abilityUnlocks: ["newAbility", "passiveTrait"],
  exampleUnlocks: [
    "Gain +1 Wit",
    "Unlock 'Second Wind' (revive once per campaign)",
    "Reroll 1 failed check per act"
  ]
};

export const STORY_STRUCTURE = {
  acts: 3,
  scenesPerAct: 3,
  branches: true,
  endings: ["good", "neutral", "bad"] as const
};

export const AI_PROMPT_FIELDS = [
  "playerClass",
  "currentStats",
  "inventory",
  "currentAct",
  "campaignSynopsis",
  "recentDecisions"
];

export const REQUIRED_BLUEPRINT_FIELDS = [
  "title",
  "setting",
  "theme",
  "keyNpcs",
  "milestones",
  "endings",
  "maxScenesPerAct"
];

export const GAME_CONSTANTS = {
  STARTING_GOLD: 50,
  STARTING_TOKENS: 0,
  STARTING_HEALTH: 100,
  LEVEL_UP_EXPERIENCE: 100,
  MAX_INVENTORY_SLOTS: 20,
  SAVE_SLOTS: 3
};
