'use client';

import React, { useState } from 'react';
import { CharacterClass, Character } from '../types/game';
import { CHARACTER_CLASSES } from '../config/gameConfig';
import { createCharacter, isValidCharacterName } from '../utils/gameUtils';

interface CharacterCreationProps {
  onCharacterCreated: (character: Character) => void;
  unlockedClasses?: string[];
}

export default function CharacterCreation({ 
  onCharacterCreated, 
  unlockedClasses = CHARACTER_CLASSES.map(c => c.name) 
}: CharacterCreationProps) {
  const [selectedClass, setSelectedClass] = useState<CharacterClass | null>(null);
  const [characterName, setCharacterName] = useState('');
  const [error, setError] = useState('');

  const availableClasses = CHARACTER_CLASSES.filter(
    cls => unlockedClasses.includes(cls.name)
  );

  const handleCreateCharacter = () => {
    if (!selectedClass) {
      setError('Please select a class');
      return;
    }

    if (!isValidCharacterName(characterName)) {
      setError('Character name must be 2-20 characters long');
      return;
    }

    const character = createCharacter(characterName.trim(), selectedClass);
    onCharacterCreated(character);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-900 text-white rounded-lg">
      <h2 className="text-3xl font-bold mb-6 text-center">Create Your Character</h2>
      
      {/* Character Name Input */}
      <div className="mb-6">
        <label className="block text-lg font-semibold mb-2">
          Character Name
        </label>
        <input
          type="text"
          value={characterName}
          onChange={(e) => {
            setCharacterName(e.target.value);
            setError('');
          }}
          className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white"
          placeholder="Enter your character's name"
          maxLength={20}
        />
      </div>

      {/* Class Selection */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-4">Choose Your Class</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {availableClasses.map((cls) => (
            <div
              key={cls.name}
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedClass?.name === cls.name
                  ? 'border-blue-500 bg-blue-900/30'
                  : 'border-gray-700 bg-gray-800 hover:border-gray-600'
              }`}
              onClick={() => {
                setSelectedClass(cls);
                setError('');
              }}
            >
              <h4 className="text-lg font-bold mb-2">{cls.name}</h4>
              
              {/* Stats */}
              <div className="mb-3">
                <div className="text-sm text-gray-300 mb-1">Stats:</div>
                <div className="flex gap-4 text-sm">
                  <span>Might: {cls.stats.might}</span>
                  <span>Wit: {cls.stats.wit}</span>
                  <span>Charm: {cls.stats.charm}</span>
                </div>
              </div>

              {/* Abilities */}
              <div>
                <div className="text-sm text-gray-300 mb-1">Starting Abilities:</div>
                <div className="flex flex-wrap gap-2">
                  {cls.abilities.map((ability) => (
                    <span
                      key={ability}
                      className="px-2 py-1 bg-gray-700 rounded text-xs"
                    >
                      {ability}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Class Description */}
      {selectedClass && (
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h4 className="text-lg font-semibold mb-2">{selectedClass.name} Description</h4>
          <p className="text-gray-300">
            {getClassDescription(selectedClass.name)}
          </p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300">
          {error}
        </div>
      )}

      {/* Create Button */}
      <div className="text-center">
        <button
          onClick={handleCreateCharacter}
          className="px-8 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold transition-colors"
          disabled={!selectedClass || !characterName.trim()}
        >
          Create Character
        </button>
      </div>
    </div>
  );
}

function getClassDescription(className: string): string {
  const descriptions = {
    Warrior: "Masters of combat and defense. Warriors excel in physical confrontations and can protect their allies with their shield and battle prowess.",
    Mage: "Wielders of arcane magic. Mages use their intelligence to solve problems and unleash devastating spells upon their enemies.",
    Rogue: "Cunning and agile. Rogues excel at stealth, deception, and finding creative solutions to problems through wit and charm.",
    Healer: "Divine servants who mend wounds and provide support. Healers keep the party alive and offer wisdom in difficult situations."
  };
  
  return descriptions[className as keyof typeof descriptions] || "A mysterious class with unknown abilities.";
}
