import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  GameState, 
  Character, 
  Campaign, 
  Decision, 
  InventoryItem, 
  GameCurrency,
  PlayerProgress 
} from '../types/game';
import { GAME_CONSTANTS } from '../config/gameConfig';

interface GameStore extends GameState {
  // Actions
  startNewGame: (character: Character, campaign: Campaign) => void;
  saveGame: () => void;
  loadGame: (saveData: any) => void;
  makeDecision: (decision: Decision) => void;
  addToInventory: (item: InventoryItem) => void;
  removeFromInventory: (itemId: string, quantity?: number) => void;
  updateCurrency: (gold: number, tokens: number) => void;
  nextScene: () => void;
  nextAct: () => void;
  endGame: () => void;
  updateCharacter: (character: Character) => void;
  addSynopsis: (synopsis: string) => void;
}

interface ProgressStore extends PlayerProgress {
  // Actions
  unlockClass: (className: string) => void;
  unlockCampaign: (campaignId: string) => void;
  unlockItem: (itemId: string) => void;
  addAchievement: (achievement: string) => void;
  completeCampaign: (campaignId: string) => void;
  updateProgress: (progress: Partial<PlayerProgress>) => void;
}

export const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentMode: { type: 'solo', saveSystem: { enabled: true } },
      currentCampaign: undefined,
      currentAct: 1,
      currentScene: 1,
      players: [],
      inventory: [],
      currency: { 
        gold: GAME_CONSTANTS.STARTING_GOLD, 
        tokens: GAME_CONSTANTS.STARTING_TOKENS 
      },
      decisionHistory: [],
      synopsisHistory: [],
      isGameActive: false,
      lastSaved: undefined,

      // Actions
      startNewGame: (character: Character, campaign: Campaign) => {
        set({
          currentCampaign: campaign,
          currentAct: 1,
          currentScene: 1,
          players: [character],
          inventory: [],
          currency: { 
            gold: GAME_CONSTANTS.STARTING_GOLD, 
            tokens: GAME_CONSTANTS.STARTING_TOKENS 
          },
          decisionHistory: [],
          synopsisHistory: [campaign.synopsis],
          isGameActive: true,
          lastSaved: Date.now()
        });
      },

      saveGame: () => {
        set({ lastSaved: Date.now() });
      },

      loadGame: (saveData: any) => {
        set({
          ...saveData,
          isGameActive: true
        });
      },

      makeDecision: (decision: Decision) => {
        const state = get();
        set({
          decisionHistory: [...state.decisionHistory, decision]
        });
      },

      addToInventory: (item: InventoryItem) => {
        const state = get();
        const existingItem = state.inventory.find(
          inv => inv.item.id === item.item.id
        );

        if (existingItem) {
          set({
            inventory: state.inventory.map(inv =>
              inv.item.id === item.item.id
                ? { ...inv, quantity: inv.quantity + item.quantity }
                : inv
            )
          });
        } else {
          set({
            inventory: [...state.inventory, item]
          });
        }
      },

      removeFromInventory: (itemId: string, quantity = 1) => {
        const state = get();
        set({
          inventory: state.inventory
            .map(inv => 
              inv.item.id === itemId
                ? { ...inv, quantity: inv.quantity - quantity }
                : inv
            )
            .filter(inv => inv.quantity > 0)
        });
      },

      updateCurrency: (gold: number, tokens: number) => {
        const state = get();
        set({
          currency: {
            gold: Math.max(0, state.currency.gold + gold),
            tokens: Math.max(0, state.currency.tokens + tokens)
          }
        });
      },

      nextScene: () => {
        const state = get();
        set({
          currentScene: state.currentScene + 1
        });
      },

      nextAct: () => {
        const state = get();
        set({
          currentAct: state.currentAct + 1,
          currentScene: 1
        });
      },

      endGame: () => {
        set({
          isGameActive: false,
          currentCampaign: undefined
        });
      },

      updateCharacter: (character: Character) => {
        const state = get();
        set({
          players: state.players.map(p => 
            p.id === character.id ? character : p
          )
        });
      },

      addSynopsis: (synopsis: string) => {
        const state = get();
        set({
          synopsisHistory: [...state.synopsisHistory, synopsis]
        });
      }
    }),
    {
      name: 'textquest-game-state',
      partialize: (state) => ({
        currentCampaign: state.currentCampaign,
        currentAct: state.currentAct,
        currentScene: state.currentScene,
        players: state.players,
        inventory: state.inventory,
        currency: state.currency,
        decisionHistory: state.decisionHistory,
        synopsisHistory: state.synopsisHistory,
        lastSaved: state.lastSaved
      })
    }
  )
);

export const useProgressStore = create<ProgressStore>()(
  persist(
    (set, get) => ({
      // Initial state
      completedCampaigns: [],
      unlockedClasses: ['Warrior', 'Mage', 'Rogue', 'Healer'],
      unlockedCampaigns: [],
      unlockedItems: [],
      currency: { gold: 0, tokens: 0 },
      achievements: [],

      // Actions
      unlockClass: (className: string) => {
        const state = get();
        if (!state.unlockedClasses.includes(className)) {
          set({
            unlockedClasses: [...state.unlockedClasses, className]
          });
        }
      },

      unlockCampaign: (campaignId: string) => {
        const state = get();
        if (!state.unlockedCampaigns.includes(campaignId)) {
          set({
            unlockedCampaigns: [...state.unlockedCampaigns, campaignId]
          });
        }
      },

      unlockItem: (itemId: string) => {
        const state = get();
        if (!state.unlockedItems.includes(itemId)) {
          set({
            unlockedItems: [...state.unlockedItems, itemId]
          });
        }
      },

      addAchievement: (achievement: string) => {
        const state = get();
        if (!state.achievements.includes(achievement)) {
          set({
            achievements: [...state.achievements, achievement]
          });
        }
      },

      completeCampaign: (campaignId: string) => {
        const state = get();
        if (!state.completedCampaigns.includes(campaignId)) {
          set({
            completedCampaigns: [...state.completedCampaigns, campaignId]
          });
        }
      },

      updateProgress: (progress: Partial<PlayerProgress>) => {
        set(progress);
      }
    }),
    {
      name: 'textquest-progress'
    }
  )
);
