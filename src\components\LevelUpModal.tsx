'use client';

import React, { useState } from 'react';
import { Character } from '../types/game';
import { ProgressionSystem, LevelUpOption } from '../systems/progressionSystem';

interface LevelUpModalProps {
  character: Character;
  isOpen: boolean;
  onLevelUp: (character: Character) => void;
  onClose: () => void;
}

export default function LevelUpModal({ character, isOpen, onLevelUp, onClose }: LevelUpModalProps) {
  const [selectedOption, setSelectedOption] = useState<LevelUpOption | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);

  if (!isOpen) return null;

  const levelUpOptions = ProgressionSystem.getLevelUpOptions(character);
  const newLevel = character.level + 1;

  const handleConfirmLevelUp = () => {
    if (!selectedOption) return;

    setIsConfirming(true);
    
    // Apply level up after a brief delay for dramatic effect
    setTimeout(() => {
      const updatedCharacter = ProgressionSystem.applyLevelUp(character, selectedOption);
      onLevelUp(updatedCharacter);
      setIsConfirming(false);
      setSelectedOption(null);
      onClose();
    }, 1500);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {!isConfirming ? (
          <>
            {/* Header */}
            <div className="text-center mb-6">
              <h2 className="text-3xl font-bold text-yellow-400 mb-2">LEVEL UP!</h2>
              <p className="text-xl text-white">
                {character.name} advances to Level {newLevel}
              </p>
              <p className="text-gray-300 mt-2">
                Choose your advancement:
              </p>
            </div>

            {/* Level Up Benefits */}
            <div className="mb-6 p-4 bg-green-900/30 border border-green-700 rounded-lg">
              <h3 className="text-lg font-semibold text-green-400 mb-2">Automatic Benefits:</h3>
              <ul className="text-green-300 space-y-1">
                <li>• +20 Maximum Health</li>
                <li>• +20 Current Health (immediate healing)</li>
                <li>• Increased power and capabilities</li>
              </ul>
            </div>

            {/* Options */}
            <div className="space-y-3 mb-6">
              <h3 className="text-lg font-semibold text-white mb-3">Choose One Advancement:</h3>
              {levelUpOptions.map((option, index) => (
                <div
                  key={index}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedOption === option
                      ? 'border-yellow-500 bg-yellow-900/30'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}
                  onClick={() => setSelectedOption(option)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-white mb-1">
                        {option.name}
                      </h4>
                      <p className="text-gray-300 text-sm mb-2">
                        {option.description}
                      </p>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          option.type === 'stat' ? 'bg-blue-700 text-blue-200' :
                          option.type === 'ability' ? 'bg-purple-700 text-purple-200' :
                          'bg-orange-700 text-orange-200'
                        }`}>
                          {option.type.toUpperCase()}
                        </span>
                        {option.type === 'stat' && option.statIncrease && (
                          <span className="text-blue-400 text-sm">
                            +{Object.values(option.statIncrease)[0]} to {Object.keys(option.statIncrease)[0]}
                          </span>
                        )}
                      </div>
                    </div>
                    {selectedOption === option && (
                      <div className="ml-4 text-yellow-400">
                        ✓
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Current Stats Display */}
            <div className="mb-6 p-4 bg-gray-700 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-2">Current Stats:</h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-red-400">{character.currentStats.might}</div>
                  <div className="text-sm text-gray-300">Might</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400">{character.currentStats.wit}</div>
                  <div className="text-sm text-gray-300">Wit</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{character.currentStats.charm}</div>
                  <div className="text-sm text-gray-300">Charm</div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 justify-center">
              <button
                onClick={handleConfirmLevelUp}
                disabled={!selectedOption}
                className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
                  selectedOption
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                Confirm Level Up
              </button>
              <button
                onClick={onClose}
                className="px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg font-semibold text-white transition-colors"
              >
                Cancel
              </button>
            </div>
          </>
        ) : (
          /* Confirmation Animation */
          <div className="text-center py-12">
            <div className="animate-pulse mb-6">
              <div className="text-6xl mb-4">⭐</div>
              <h2 className="text-3xl font-bold text-yellow-400 mb-2">
                Leveling Up...
              </h2>
              <p className="text-xl text-white">
                {character.name} grows stronger!
              </p>
            </div>
            
            {selectedOption && (
              <div className="mt-6 p-4 bg-yellow-900/30 border border-yellow-700 rounded-lg">
                <p className="text-yellow-300">
                  Applying: <strong>{selectedOption.name}</strong>
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
