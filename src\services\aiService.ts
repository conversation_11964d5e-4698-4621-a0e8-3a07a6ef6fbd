import OpenAI from 'openai';
import { Character, Scene, Choice, AIPromptContext, Campaign, StoryArc } from '../types/game';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, API calls should go through your backend
});

export interface AISceneRequest {
  context: AIPromptContext;
  campaign: Campaign;
  previousScene?: Scene;
  actNumber: number;
  sceneNumber: number;
  storyDirection?: 'continue' | 'escalate' | 'resolve';
}

export interface AISceneResponse {
  scene: Scene;
  synopsis: string;
  suggestedNextDirection: string;
}

export class AIService {
  static async generateScene(request: AISceneRequest): Promise<AISceneResponse> {
    const prompt = this.buildScenePrompt(request);
    
    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: this.getSystemPrompt()
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.8,
        max_tokens: 1500
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from AI');
      }

      return this.parseAIResponse(response, request);
    } catch (error) {
      console.error('AI Service Error:', error);
      return this.getFallbackScene(request);
    }
  }

  static async generateCampaign(
    theme: string, 
    setting: string, 
    playerClass: string
  ): Promise<StoryArc> {
    const prompt = `Create a 3-act campaign for a ${playerClass} character.
    
Theme: ${theme}
Setting: ${setting}

Please create a campaign with:
- A compelling title
- 3-4 key NPCs
- Major story milestones for each act
- Multiple possible endings (good, neutral, bad)
- Rich setting details

Format your response as a structured story outline.`;

    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a master storyteller creating D&D-style adventures. Create engaging, branching narratives with meaningful choices."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.9,
        max_tokens: 2000
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No campaign response from AI');
      }

      return this.parseCampaignResponse(response, theme, setting);
    } catch (error) {
      console.error('Campaign Generation Error:', error);
      return this.getFallbackCampaign(theme, setting);
    }
  }

  private static getSystemPrompt(): string {
    return `You are an AI Game Master for TextQuest, a text-based RPG with D&D elements.

Your role:
- Create engaging, immersive scenes with rich descriptions
- Provide 3 meaningful choices that reflect different approaches (combat, stealth, social, etc.)
- Include appropriate ability checks based on character stats
- Maintain narrative consistency and pacing
- Create branching storylines that respond to player decisions

Guidelines:
- Scenes should be 2-3 paragraphs of vivid description
- Each choice should have clear consequences and require different stats
- Ability check difficulties: Easy (10-12), Medium (13-15), Hard (16-18), Extreme (19+)
- Include atmospheric details and character moments
- Maintain the fantasy adventure tone

Response Format:
TITLE: [Scene Title]
DESCRIPTION: [2-3 paragraph scene description]
CHOICE1: [Choice text] | STAT: [might/wit/charm] | DIFFICULTY: [number] | SUCCESS: [outcome] | FAILURE: [outcome]
CHOICE2: [Choice text] | STAT: [might/wit/charm] | DIFFICULTY: [number] | SUCCESS: [outcome] | FAILURE: [outcome]  
CHOICE3: [Choice text] | STAT: [might/wit/charm] | DIFFICULTY: [number] | SUCCESS: [outcome] | FAILURE: [outcome]
SYNOPSIS: [Brief summary of what happened]
NEXT: [Suggestion for next scene direction]`;
  }

  private static buildScenePrompt(request: AISceneRequest): string {
    const { context, campaign, actNumber, sceneNumber, storyDirection } = request;
    
    return `Generate Act ${actNumber}, Scene ${sceneNumber} for the campaign "${campaign.title}".

Campaign Context:
- Setting: ${campaign.setting}
- Theme: ${campaign.theme}
- Key NPCs: ${campaign.keyNpcs.join(', ')}
- Synopsis so far: ${context.campaignSynopsis}

Character Context:
- Class: ${context.playerClass}
- Stats: Might ${context.currentStats.might}, Wit ${context.currentStats.wit}, Charm ${context.currentStats.charm}
- Recent Decisions: ${context.recentDecisions.slice(-3).map(d => d.choice).join('; ')}
- Inventory: ${context.inventory.map(i => i.item.name).join(', ') || 'None'}

Story Direction: ${storyDirection || 'continue'}

Create a scene that:
1. Builds on previous events and decisions
2. Offers meaningful choices that utilize different character stats
3. Advances the overall narrative toward the act's climax
4. Includes rich atmospheric details and character development opportunities`;
  }

  private static parseAIResponse(response: string, request: AISceneRequest): AISceneResponse {
    const lines = response.split('\n').filter(line => line.trim());
    
    let title = 'Untitled Scene';
    let description = 'A mysterious scene unfolds before you.';
    let choices: Choice[] = [];
    let synopsis = 'The adventure continues.';
    let suggestedNextDirection = 'continue';

    for (const line of lines) {
      if (line.startsWith('TITLE:')) {
        title = line.replace('TITLE:', '').trim();
      } else if (line.startsWith('DESCRIPTION:')) {
        description = line.replace('DESCRIPTION:', '').trim();
      } else if (line.startsWith('CHOICE')) {
        const choice = this.parseChoice(line);
        if (choice) choices.push(choice);
      } else if (line.startsWith('SYNOPSIS:')) {
        synopsis = line.replace('SYNOPSIS:', '').trim();
      } else if (line.startsWith('NEXT:')) {
        suggestedNextDirection = line.replace('NEXT:', '').trim();
      }
    }

    // Ensure we have at least 3 choices
    while (choices.length < 3) {
      choices.push({
        id: `fallback-${choices.length}`,
        text: 'Continue forward cautiously',
        consequence: 'You proceed with caution.',
        requiresCheck: {
          stat: 'wit',
          difficulty: 12,
          successText: 'Your careful approach pays off.',
          failureText: 'Despite your caution, complications arise.'
        }
      });
    }

    const scene: Scene = {
      id: `scene-${request.actNumber}-${request.sceneNumber}`,
      title,
      description,
      choices: choices.slice(0, 3), // Limit to 3 choices
      isGenerated: true
    };

    return {
      scene,
      synopsis,
      suggestedNextDirection
    };
  }

  private static parseChoice(line: string): Choice | null {
    try {
      const parts = line.split('|').map(p => p.trim());
      const choiceText = parts[0].replace(/CHOICE\d+:/, '').trim();
      
      let stat: 'might' | 'wit' | 'charm' = 'wit';
      let difficulty = 12;
      let successText = 'You succeed!';
      let failureText = 'You fail.';

      for (const part of parts) {
        if (part.startsWith('STAT:')) {
          const statValue = part.replace('STAT:', '').trim().toLowerCase();
          if (['might', 'wit', 'charm'].includes(statValue)) {
            stat = statValue as 'might' | 'wit' | 'charm';
          }
        } else if (part.startsWith('DIFFICULTY:')) {
          difficulty = parseInt(part.replace('DIFFICULTY:', '').trim()) || 12;
        } else if (part.startsWith('SUCCESS:')) {
          successText = part.replace('SUCCESS:', '').trim();
        } else if (part.startsWith('FAILURE:')) {
          failureText = part.replace('FAILURE:', '').trim();
        }
      }

      return {
        id: `choice-${Date.now()}-${Math.random()}`,
        text: choiceText,
        consequence: successText,
        requiresCheck: {
          stat,
          difficulty,
          successText,
          failureText
        }
      };
    } catch (error) {
      console.error('Error parsing choice:', error);
      return null;
    }
  }

  private static parseCampaignResponse(response: string, theme: string, setting: string): StoryArc {
    // This would parse the AI response into a proper StoryArc
    // For now, return a basic structure
    return {
      id: `campaign-${Date.now()}`,
      title: `The ${theme} of ${setting}`,
      setting,
      theme,
      keyNpcs: ['Mysterious Guide', 'Ancient Guardian', 'Dark Adversary'],
      milestones: ['Discovery', 'Confrontation', 'Resolution'],
      endings: ['good', 'neutral', 'bad'],
      maxScenesPerAct: 4
    };
  }

  private static getFallbackScene(request: AISceneRequest): AISceneResponse {
    return {
      scene: {
        id: `fallback-${request.actNumber}-${request.sceneNumber}`,
        title: 'A Crossroads',
        description: 'You find yourself at a crossroads, with paths leading in different directions. The air is thick with possibility and danger.',
        choices: [
          {
            id: 'fallback-1',
            text: 'Take the left path through the dark woods',
            consequence: 'You venture into the unknown.',
            requiresCheck: {
              stat: 'might',
              difficulty: 13,
              successText: 'Your strength serves you well on the treacherous path.',
              failureText: 'The path proves more challenging than expected.'
            }
          },
          {
            id: 'fallback-2',
            text: 'Study the paths carefully before choosing',
            consequence: 'You take time to analyze your options.',
            requiresCheck: {
              stat: 'wit',
              difficulty: 14,
              successText: 'Your careful observation reveals hidden details.',
              failureText: 'Despite your analysis, the choice remains unclear.'
            }
          },
          {
            id: 'fallback-3',
            text: 'Call out to see if anyone can offer guidance',
            consequence: 'You seek help from others.',
            requiresCheck: {
              stat: 'charm',
              difficulty: 12,
              successText: 'A helpful traveler appears and offers advice.',
              failureText: 'Your call echoes unanswered in the wilderness.'
            }
          }
        ],
        isGenerated: true
      },
      synopsis: 'The adventurer faces a choice of paths.',
      suggestedNextDirection: 'continue'
    };
  }

  private static getFallbackCampaign(theme: string, setting: string): StoryArc {
    return {
      id: `fallback-campaign-${Date.now()}`,
      title: `The ${theme} Chronicles`,
      setting,
      theme,
      keyNpcs: ['The Mentor', 'The Rival', 'The Guardian'],
      milestones: ['The Call to Adventure', 'The Great Challenge', 'The Final Confrontation'],
      endings: ['good', 'neutral', 'bad'],
      maxScenesPerAct: 4
    };
  }
}
