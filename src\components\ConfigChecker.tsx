'use client';

import React from 'react';

export default function ConfigChecker() {
  const firebaseVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID'
  ];

  const openaiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;
  const firebaseConfig = firebaseVars.map(varName => ({
    name: varName,
    value: process.env[varName],
    isSet: !!process.env[varName]
  }));

  const allFirebaseSet = firebaseConfig.every(config => config.isSet);

  return (
    <div className="p-4 bg-gray-800 rounded-lg text-white">
      <h3 className="text-lg font-bold mb-4">Configuration Status</h3>
      
      {/* OpenAI Status */}
      <div className="mb-4">
        <h4 className="font-semibold mb-2">OpenAI API</h4>
        <div className={`flex items-center gap-2 ${openaiKey ? 'text-green-400' : 'text-red-400'}`}>
          <span>{openaiKey ? '✅' : '❌'}</span>
          <span>NEXT_PUBLIC_OPENAI_API_KEY: {openaiKey ? 'Set' : 'Missing'}</span>
        </div>
      </div>

      {/* Firebase Status */}
      <div>
        <h4 className="font-semibold mb-2">Firebase Configuration</h4>
        <div className="space-y-1">
          {firebaseConfig.map((config) => (
            <div key={config.name} className={`flex items-center gap-2 text-sm ${
              config.isSet ? 'text-green-400' : 'text-red-400'
            }`}>
              <span>{config.isSet ? '✅' : '❌'}</span>
              <span>{config.name}: {config.isSet ? 'Set' : 'Missing'}</span>
            </div>
          ))}
        </div>
        
        <div className={`mt-2 p-2 rounded text-sm ${
          allFirebaseSet ? 'bg-green-900/30 text-green-300' : 'bg-red-900/30 text-red-300'
        }`}>
          {allFirebaseSet ? 
            '✅ Firebase fully configured' : 
            '❌ Firebase incomplete - some variables missing'
          }
        </div>
      </div>

      {/* Instructions */}
      {(!openaiKey || !allFirebaseSet) && (
        <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700 rounded text-yellow-300 text-sm">
          <p className="font-semibold mb-1">Setup Instructions:</p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Copy <code>.env.local.example</code> to <code>.env.local</code></li>
            <li>Add your API keys to <code>.env.local</code></li>
            <li>Restart the development server</li>
            <li>See <code>SETUP_GUIDE.md</code> for detailed instructions</li>
          </ol>
        </div>
      )}
    </div>
  );
}
