'use client';

import React, { useState } from 'react';
import { Character, Scene, Choice, Decision } from '../types/game';
import { useGameStore } from '../store/gameStore';
import { rollAbilityCheck, getHealthPercentage } from '../utils/gameUtils';

interface GameScreenProps {
  character: Character;
  currentScene: Scene;
  onChoiceMade: (choice: Choice, decision: Decision) => void;
}

export default function GameScreen({ character, currentScene, onChoiceMade }: GameScreenProps) {
  const [selectedChoice, setSelectedChoice] = useState<Choice | null>(null);
  const [abilityCheckResult, setAbilityCheckResult] = useState<any>(null);
  const [isProcessingChoice, setIsProcessingChoice] = useState(false);

  const { currentAct, currentScene: sceneNumber, currency } = useGameStore();

  const handleChoiceSelect = (choice: Choice) => {
    setSelectedChoice(choice);
    setAbilityCheckResult(null);
  };

  const handleChoiceConfirm = async () => {
    if (!selectedChoice) return;

    setIsProcessingChoice(true);

    let checkResult = null;
    let outcome = selectedChoice.consequence;

    // Handle ability check if required
    if (selectedChoice.requiresCheck) {
      checkResult = rollAbilityCheck(character, selectedChoice.requiresCheck);
      setAbilityCheckResult(checkResult);
      
      outcome = checkResult.success 
        ? selectedChoice.requiresCheck.successText 
        : selectedChoice.requiresCheck.failureText;
    }

    // Create decision record
    const decision: Decision = {
      sceneId: currentScene.id,
      choice: selectedChoice.text,
      outcome,
      timestamp: Date.now()
    };

    // Wait a moment for dramatic effect
    setTimeout(() => {
      onChoiceMade(selectedChoice, decision);
      setIsProcessingChoice(false);
      setSelectedChoice(null);
      setAbilityCheckResult(null);
    }, 2000);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-900 text-white min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6 p-4 bg-gray-800 rounded-lg">
        <div>
          <h1 className="text-2xl font-bold">{character.name}</h1>
          <p className="text-gray-300">{character.class.name} - Level {character.level}</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-300">Act {currentAct} - Scene {sceneNumber}</p>
          <p className="text-sm text-yellow-400">{currency.gold}g {currency.tokens}t</p>
        </div>
      </div>

      {/* Character Stats */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold">Character Status</h3>
          <div className="text-sm">
            HP: {character.health}/{character.maxHealth} ({getHealthPercentage(character)}%)
          </div>
        </div>
        
        {/* Health Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
          <div 
            className="bg-red-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${getHealthPercentage(character)}%` }}
          />
        </div>

        {/* Stats */}
        <div className="flex gap-6 text-sm">
          <span>Might: {character.currentStats.might}</span>
          <span>Wit: {character.currentStats.wit}</span>
          <span>Charm: {character.currentStats.charm}</span>
        </div>
      </div>

      {/* Scene Content */}
      <div className="mb-6 p-6 bg-gray-800 rounded-lg">
        <h2 className="text-xl font-bold mb-4">{currentScene.title}</h2>
        <div className="text-gray-300 leading-relaxed whitespace-pre-wrap">
          {currentScene.description}
        </div>
      </div>

      {/* Ability Check Result */}
      {abilityCheckResult && (
        <div className="mb-6 p-4 bg-blue-900/50 border border-blue-700 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Ability Check Result</h3>
          <div className="text-sm">
            <p>Roll: {abilityCheckResult.roll} + {character.currentStats[selectedChoice?.requiresCheck?.stat || 'might']} = {abilityCheckResult.total}</p>
            <p className={`font-semibold ${abilityCheckResult.success ? 'text-green-400' : 'text-red-400'}`}>
              {abilityCheckResult.success ? 'SUCCESS!' : 'FAILURE!'}
            </p>
          </div>
        </div>
      )}

      {/* Choices */}
      {!isProcessingChoice && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">What do you do?</h3>
          <div className="space-y-3">
            {currentScene.choices.map((choice, index) => (
              <div
                key={choice.id}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedChoice?.id === choice.id
                    ? 'border-blue-500 bg-blue-900/30'
                    : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                }`}
                onClick={() => handleChoiceSelect(choice)}
              >
                <div className="flex justify-between items-start">
                  <p className="flex-1">{choice.text}</p>
                  {choice.requiresCheck && (
                    <span className="ml-3 px-2 py-1 bg-yellow-700 text-yellow-200 rounded text-xs">
                      {choice.requiresCheck.stat.toUpperCase()} {choice.requiresCheck.difficulty}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Confirm Choice Button */}
      {selectedChoice && !isProcessingChoice && (
        <div className="text-center">
          <button
            onClick={handleChoiceConfirm}
            className="px-8 py-3 bg-green-600 hover:bg-green-700 rounded-lg font-semibold transition-colors"
          >
            Confirm Choice
          </button>
        </div>
      )}

      {/* Processing State */}
      {isProcessingChoice && (
        <div className="text-center p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-300">Processing your choice...</p>
        </div>
      )}

      {/* Abilities */}
      <div className="mt-6 p-4 bg-gray-800 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Available Abilities</h3>
        <div className="flex flex-wrap gap-2">
          {character.abilities.map((ability) => (
            <button
              key={ability}
              className="px-3 py-2 bg-purple-700 hover:bg-purple-600 rounded text-sm transition-colors"
              onClick={() => {
                // TODO: Implement ability usage
                console.log(`Using ability: ${ability}`);
              }}
            >
              {ability}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
