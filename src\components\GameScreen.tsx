'use client';

import React, { useState } from 'react';
import { Character, Scene, Choice, Decision } from '../types/game';
import { useGameStore } from '../store/gameStore';
import { rollAbilityCheck, getHealthPercentage } from '../utils/gameUtils';
import { ProgressionSystem } from '../systems/progressionSystem';
import { AbilitySystem, Ability } from '../systems/abilitySystem';
import LevelUpModal from './LevelUpModal';
import CharacterSheet from './CharacterSheet';

interface GameScreenProps {
  character: Character;
  currentScene: Scene;
  onChoiceMade: (choice: Choice, decision: Decision) => void;
}

export default function GameScreen({ character, currentScene, onChoiceMade }: GameScreenProps) {
  const [selectedChoice, setSelectedChoice] = useState<Choice | null>(null);
  const [abilityCheckResult, setAbilityCheckResult] = useState<any>(null);
  const [isProcessingChoice, setIsProcessingChoice] = useState(false);
  const [showLevelUpModal, setShowLevelUpModal] = useState(false);
  const [showCharacterSheet, setShowCharacterSheet] = useState(false);
  const [currentCharacter, setCurrentCharacter] = useState<Character>(character);

  const { currentAct, currentScene: sceneNumber, currency, updateCharacter } = useGameStore();

  // Check for level up on component mount and character updates
  React.useEffect(() => {
    if (ProgressionSystem.canLevelUp(currentCharacter)) {
      setShowLevelUpModal(true);
    }
  }, [currentCharacter]);

  const handleChoiceSelect = (choice: Choice) => {
    setSelectedChoice(choice);
    setAbilityCheckResult(null);
  };

  const handleChoiceConfirm = async () => {
    if (!selectedChoice) return;

    setIsProcessingChoice(true);

    let checkResult = null;
    let outcome = selectedChoice.consequence;

    // Handle ability check if required
    if (selectedChoice.requiresCheck) {
      checkResult = rollAbilityCheck(currentCharacter, selectedChoice.requiresCheck);
      setAbilityCheckResult(checkResult);

      outcome = checkResult.success
        ? selectedChoice.requiresCheck.successText
        : selectedChoice.requiresCheck.failureText;
    }

    // Grant experience based on choice difficulty
    let experienceGained = 0;
    if (selectedChoice.requiresCheck) {
      const difficulty = selectedChoice.requiresCheck.difficulty;
      if (difficulty >= 20) experienceGained = 50;
      else if (difficulty >= 15) experienceGained = 25;
      else experienceGained = 10;
    } else {
      experienceGained = 5; // Base experience for making a choice
    }

    // Update character with experience
    const updatedCharacter = ProgressionSystem.grantExperience(currentCharacter, experienceGained);
    setCurrentCharacter(updatedCharacter);
    updateCharacter(updatedCharacter);

    // Create decision record
    const decision: Decision = {
      sceneId: currentScene.id,
      choice: selectedChoice.text,
      outcome,
      timestamp: Date.now()
    };

    // Wait a moment for dramatic effect
    setTimeout(() => {
      onChoiceMade(selectedChoice, decision);
      setIsProcessingChoice(false);
      setSelectedChoice(null);
      setAbilityCheckResult(null);
    }, 2000);
  };

  const handleLevelUp = (leveledUpCharacter: Character) => {
    setCurrentCharacter(leveledUpCharacter);
    updateCharacter(leveledUpCharacter);
    setShowLevelUpModal(false);
  };

  const handleUseAbility = (ability: Ability) => {
    const result = AbilitySystem.useAbility(currentCharacter, ability);

    if (result.success) {
      // Apply ability effects to character
      let updatedCharacter = { ...currentCharacter };

      if (result.healing) {
        updatedCharacter.health = Math.min(
          updatedCharacter.maxHealth,
          updatedCharacter.health + result.healing
        );
      }

      setCurrentCharacter(updatedCharacter);
      updateCharacter(updatedCharacter);

      // Show result message (in a real game, this would be more sophisticated)
      alert(result.message);
    } else {
      alert(result.message);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-900 text-white min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6 p-4 bg-gray-800 rounded-lg">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">{currentCharacter.name}</h1>
            <p className="text-gray-300">{currentCharacter.class.name} - Level {currentCharacter.level}</p>
          </div>
          {ProgressionSystem.canLevelUp(currentCharacter) && (
            <div className="animate-pulse">
              <span className="px-3 py-1 bg-yellow-600 text-yellow-100 rounded-full text-sm font-semibold">
                LEVEL UP!
              </span>
            </div>
          )}
        </div>
        <div className="text-right flex items-center gap-4">
          <div>
            <p className="text-sm text-gray-300">Act {currentAct} - Scene {sceneNumber}</p>
            <p className="text-sm text-yellow-400">{currency.gold}g {currency.tokens}t</p>
          </div>
          <button
            onClick={() => setShowCharacterSheet(true)}
            className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm font-semibold transition-colors"
          >
            Character
          </button>
        </div>
      </div>

      {/* Character Stats */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold">Character Status</h3>
          <div className="text-sm">
            HP: {currentCharacter.health}/{currentCharacter.maxHealth} ({getHealthPercentage(currentCharacter)}%)
          </div>
        </div>

        {/* Health Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
          <div
            className="bg-red-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${getHealthPercentage(currentCharacter)}%` }}
          />
        </div>

        {/* Stats and Experience */}
        <div className="flex justify-between items-center">
          <div className="flex gap-6 text-sm">
            <span>Might: {currentCharacter.currentStats.might}</span>
            <span>Wit: {currentCharacter.currentStats.wit}</span>
            <span>Charm: {currentCharacter.currentStats.charm}</span>
          </div>

          {/* Experience Progress */}
          {currentCharacter.level < 5 && (
            <div className="text-right">
              <div className="text-xs text-gray-400 mb-1">
                XP: {currentCharacter.experience}/{ProgressionSystem.getExperienceForLevel(currentCharacter.level + 1)}
              </div>
              <div className="w-24 bg-gray-700 rounded-full h-1">
                <div
                  className="bg-yellow-500 h-1 rounded-full transition-all duration-300"
                  style={{ width: `${ProgressionSystem.getProgressToNextLevel(currentCharacter).percentage}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Scene Content */}
      <div className="mb-6 p-6 bg-gray-800 rounded-lg">
        <h2 className="text-xl font-bold mb-4">{currentScene.title}</h2>
        <div className="text-gray-300 leading-relaxed whitespace-pre-wrap">
          {currentScene.description}
        </div>
      </div>

      {/* Ability Check Result */}
      {abilityCheckResult && (
        <div className="mb-6 p-4 bg-blue-900/50 border border-blue-700 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Ability Check Result</h3>
          <div className="text-sm">
            <p>Roll: {abilityCheckResult.roll} + {character.currentStats[selectedChoice?.requiresCheck?.stat || 'might']} = {abilityCheckResult.total}</p>
            <p className={`font-semibold ${abilityCheckResult.success ? 'text-green-400' : 'text-red-400'}`}>
              {abilityCheckResult.success ? 'SUCCESS!' : 'FAILURE!'}
            </p>
          </div>
        </div>
      )}

      {/* Choices */}
      {!isProcessingChoice && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">What do you do?</h3>
          <div className="space-y-3">
            {currentScene.choices.map((choice, index) => (
              <div
                key={choice.id}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedChoice?.id === choice.id
                    ? 'border-blue-500 bg-blue-900/30'
                    : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                }`}
                onClick={() => handleChoiceSelect(choice)}
              >
                <div className="flex justify-between items-start">
                  <p className="flex-1">{choice.text}</p>
                  {choice.requiresCheck && (
                    <div className="ml-3 flex items-center gap-2">
                      <span className="px-2 py-1 bg-yellow-700 text-yellow-200 rounded text-xs">
                        {choice.requiresCheck.stat.toUpperCase()} {choice.requiresCheck.difficulty}
                      </span>
                      <span className="text-xs text-gray-400">
                        (+{currentCharacter.currentStats[choice.requiresCheck.stat]} bonus)
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Confirm Choice Button */}
      {selectedChoice && !isProcessingChoice && (
        <div className="text-center">
          <button
            onClick={handleChoiceConfirm}
            className="px-8 py-3 bg-green-600 hover:bg-green-700 rounded-lg font-semibold transition-colors"
          >
            Confirm Choice
          </button>
        </div>
      )}

      {/* Processing State */}
      {isProcessingChoice && (
        <div className="text-center p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-300">Processing your choice...</p>
        </div>
      )}

      {/* Abilities */}
      <div className="mt-6 p-4 bg-gray-800 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Available Abilities</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {AbilitySystem.getCharacterAbilities(currentCharacter).map((ability, index) => (
            <div key={index} className="p-3 bg-gray-700 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-semibold text-white">{ability.name}</h4>
                <span className="text-xs text-gray-300">
                  {ability.currentUses}/{ability.usesPerAct}
                </span>
              </div>
              <p className="text-xs text-gray-300 mb-2">{ability.description}</p>
              <button
                onClick={() => handleUseAbility(ability)}
                disabled={!AbilitySystem.canUseAbility(ability)}
                className={`w-full px-3 py-1 rounded text-sm font-semibold transition-colors ${
                  AbilitySystem.canUseAbility(ability)
                    ? 'bg-purple-600 hover:bg-purple-700 text-white'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                {AbilitySystem.canUseAbility(ability) ? 'Use Ability' : 'No Uses Left'}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Modals */}
      <LevelUpModal
        character={currentCharacter}
        isOpen={showLevelUpModal}
        onLevelUp={handleLevelUp}
        onClose={() => setShowLevelUpModal(false)}
      />

      <CharacterSheet
        character={currentCharacter}
        isOpen={showCharacterSheet}
        onClose={() => setShowCharacterSheet(false)}
        onUseAbility={handleUseAbility}
      />
    </div>
  );
}
