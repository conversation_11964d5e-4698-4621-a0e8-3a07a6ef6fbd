import { Character, AbilityCheck, GameStats } from '../types/game';
import { rollAbilityCheck } from '../utils/gameUtils';

export interface Ability {
  id: string;
  name: string;
  description: string;
  cooldown: number;
  usesPerAct: number;
  currentUses: number;
  effect: AbilityEffect;
}

export interface AbilityEffect {
  type: 'damage' | 'heal' | 'buff' | 'debuff' | 'utility';
  value?: number;
  duration?: number;
  statModifier?: Partial<GameStats>;
  specialEffect?: string;
}

export interface AbilityResult {
  success: boolean;
  damage?: number;
  healing?: number;
  message: string;
  effectApplied?: boolean;
}

// Define all character abilities
export const ABILITIES: Record<string, Ability> = {
  // Warrior Abilities
  'shield-bash': {
    id: 'shield-bash',
    name: 'Shield Bash',
    description: 'Strike with your shield, dealing damage and potentially stunning the enemy.',
    cooldown: 0,
    usesPerAct: 3,
    currentUses: 3,
    effect: {
      type: 'damage',
      value: 15,
      specialEffect: 'May stun enemy for 1 turn'
    }
  },
  'battle-cry': {
    id: 'battle-cry',
    name: 'Battle Cry',
    description: 'Rally yourself and allies, boosting Might for the next encounter.',
    cooldown: 1,
    usesPerAct: 2,
    currentUses: 2,
    effect: {
      type: 'buff',
      duration: 3,
      statModifier: { might: 2 },
      specialEffect: 'Affects all party members'
    }
  },

  // Mage Abilities
  'firebolt': {
    id: 'firebolt',
    name: 'Firebolt',
    description: 'Launch a bolt of fire at your enemy, dealing magical damage.',
    cooldown: 0,
    usesPerAct: 4,
    currentUses: 4,
    effect: {
      type: 'damage',
      value: 20,
      specialEffect: 'Ignores armor'
    }
  },
  'arcane-insight': {
    id: 'arcane-insight',
    name: 'Arcane Insight',
    description: 'Gain mystical knowledge, boosting Wit for upcoming challenges.',
    cooldown: 1,
    usesPerAct: 2,
    currentUses: 2,
    effect: {
      type: 'buff',
      duration: 3,
      statModifier: { wit: 3 },
      specialEffect: 'Reveals hidden information'
    }
  },

  // Rogue Abilities
  'backstab': {
    id: 'backstab',
    name: 'Backstab',
    description: 'Strike from the shadows for massive damage.',
    cooldown: 1,
    usesPerAct: 2,
    currentUses: 2,
    effect: {
      type: 'damage',
      value: 25,
      specialEffect: 'Critical hit if enemy is unaware'
    }
  },
  'smoke-step': {
    id: 'smoke-step',
    name: 'Smoke Step',
    description: 'Vanish in a puff of smoke, avoiding the next attack and gaining positioning.',
    cooldown: 2,
    usesPerAct: 1,
    currentUses: 1,
    effect: {
      type: 'utility',
      specialEffect: 'Avoid next attack, gain advantage on next action'
    }
  },

  // Healer Abilities
  'heal': {
    id: 'heal',
    name: 'Heal',
    description: 'Channel divine energy to restore health to yourself or an ally.',
    cooldown: 0,
    usesPerAct: 3,
    currentUses: 3,
    effect: {
      type: 'heal',
      value: 30,
      specialEffect: 'Can target any party member'
    }
  },
  'divine-barrier': {
    id: 'divine-barrier',
    name: 'Divine Barrier',
    description: 'Create a protective barrier that absorbs incoming damage.',
    cooldown: 2,
    usesPerAct: 1,
    currentUses: 1,
    effect: {
      type: 'buff',
      duration: 3,
      value: 20,
      specialEffect: 'Absorbs damage until depleted'
    }
  }
};

export class AbilitySystem {
  static getCharacterAbilities(character: Character): Ability[] {
    return character.abilities.map(abilityName => {
      const abilityId = abilityName.toLowerCase().replace(/\s+/g, '-');
      return { ...ABILITIES[abilityId] };
    }).filter(Boolean);
  }

  static canUseAbility(ability: Ability): boolean {
    return ability.currentUses > 0;
  }

  static useAbility(
    character: Character, 
    ability: Ability, 
    target?: Character
  ): AbilityResult {
    if (!this.canUseAbility(ability)) {
      return {
        success: false,
        message: `${ability.name} has no uses remaining this act.`
      };
    }

    // Consume use
    ability.currentUses--;

    // Apply ability effect
    switch (ability.effect.type) {
      case 'damage':
        return this.applyDamageAbility(character, ability, target);
      case 'heal':
        return this.applyHealAbility(character, ability, target || character);
      case 'buff':
        return this.applyBuffAbility(character, ability, target || character);
      case 'utility':
        return this.applyUtilityAbility(character, ability);
      default:
        return {
          success: true,
          message: `${character.name} uses ${ability.name}!`
        };
    }
  }

  private static applyDamageAbility(
    caster: Character, 
    ability: Ability, 
    target?: Character
  ): AbilityResult {
    const baseDamage = ability.effect.value || 0;
    const statBonus = Math.floor(caster.currentStats.might / 2);
    const totalDamage = baseDamage + statBonus;

    return {
      success: true,
      damage: totalDamage,
      message: `${caster.name} uses ${ability.name} for ${totalDamage} damage! ${ability.effect.specialEffect || ''}`
    };
  }

  private static applyHealAbility(
    caster: Character, 
    ability: Ability, 
    target: Character
  ): AbilityResult {
    const baseHealing = ability.effect.value || 0;
    const statBonus = Math.floor(caster.currentStats.charm / 2);
    const totalHealing = baseHealing + statBonus;

    return {
      success: true,
      healing: totalHealing,
      message: `${caster.name} uses ${ability.name} to heal ${target.name} for ${totalHealing} HP!`
    };
  }

  private static applyBuffAbility(
    caster: Character, 
    ability: Ability, 
    target: Character
  ): AbilityResult {
    return {
      success: true,
      effectApplied: true,
      message: `${caster.name} uses ${ability.name} on ${target.name}! ${ability.effect.specialEffect || ''}`
    };
  }

  private static applyUtilityAbility(
    caster: Character, 
    ability: Ability
  ): AbilityResult {
    return {
      success: true,
      effectApplied: true,
      message: `${caster.name} uses ${ability.name}! ${ability.effect.specialEffect || ''}`
    };
  }

  static resetAbilityUses(character: Character): void {
    // Reset all ability uses at the start of each act
    character.abilities.forEach(abilityName => {
      const abilityId = abilityName.toLowerCase().replace(/\s+/g, '-');
      const ability = ABILITIES[abilityId];
      if (ability) {
        ability.currentUses = ability.usesPerAct;
      }
    });
  }

  static getAbilityDescription(abilityName: string): string {
    const abilityId = abilityName.toLowerCase().replace(/\s+/g, '-');
    const ability = ABILITIES[abilityId];
    return ability ? ability.description : 'Unknown ability';
  }
}
