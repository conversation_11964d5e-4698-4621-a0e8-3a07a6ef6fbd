'use client';

import React, { useState } from 'react';
import { Character, Scene, Campaign, AIPromptContext } from '../types/game';
import { AIService, AISceneRequest } from '../services/aiService';
import { useGameStore } from '../store/gameStore';

interface AISceneGeneratorProps {
  character: Character;
  campaign: Campaign;
  currentAct: number;
  currentScene: number;
  onSceneGenerated: (scene: Scene, synopsis: string) => void;
  onError?: (error: string) => void;
}

export default function AISceneGenerator({
  character,
  campaign,
  currentAct,
  currentScene,
  onSceneGenerated,
  onError
}: AISceneGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [storyDirection, setStoryDirection] = useState<'continue' | 'escalate' | 'resolve'>('continue');
  
  const { inventory, decisionHistory, synopsisHistory } = useGameStore();

  const generateScene = async () => {
    setIsGenerating(true);

    try {
      const context: AIPromptContext = {
        playerClass: character.class.name,
        currentStats: character.currentStats,
        inventory,
        currentAct,
        campaignSynopsis: synopsisHistory.join(' '),
        recentDecisions: decisionHistory.slice(-5) // Last 5 decisions
      };

      const request: AISceneRequest = {
        context,
        campaign,
        actNumber: currentAct,
        sceneNumber: currentScene,
        storyDirection
      };

      const response = await AIService.generateScene(request);
      onSceneGenerated(response.scene, response.synopsis);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate scene';
      console.error('Scene generation error:', error);
      onError?.(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="p-6 bg-gray-800 rounded-lg">
      <h3 className="text-xl font-bold text-white mb-4">AI Scene Generator</h3>
      
      {/* Story Direction Selection */}
      <div className="mb-4">
        <label className="block text-sm font-semibold text-gray-300 mb-2">
          Story Direction:
        </label>
        <div className="flex gap-2">
          {[
            { value: 'continue', label: 'Continue', desc: 'Normal story progression' },
            { value: 'escalate', label: 'Escalate', desc: 'Increase tension and stakes' },
            { value: 'resolve', label: 'Resolve', desc: 'Move toward resolution' }
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setStoryDirection(option.value as any)}
              className={`px-3 py-2 rounded text-sm font-semibold transition-colors ${
                storyDirection === option.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              title={option.desc}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Current Context Display */}
      <div className="mb-4 p-3 bg-gray-700 rounded text-sm">
        <h4 className="font-semibold text-white mb-2">Current Context:</h4>
        <div className="text-gray-300 space-y-1">
          <div>Act {currentAct}, Scene {currentScene}</div>
          <div>Character: {character.name} ({character.class.name}, Level {character.level})</div>
          <div>Recent Decisions: {decisionHistory.slice(-3).length}</div>
          <div>Inventory Items: {inventory.length}</div>
        </div>
      </div>

      {/* Generate Button */}
      <button
        onClick={generateScene}
        disabled={isGenerating}
        className={`w-full py-3 rounded-lg font-semibold transition-colors ${
          isGenerating
            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
            : 'bg-purple-600 hover:bg-purple-700 text-white'
        }`}
      >
        {isGenerating ? (
          <div className="flex items-center justify-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            Generating Scene...
          </div>
        ) : (
          'Generate AI Scene'
        )}
      </button>

      {/* Info */}
      <div className="mt-3 text-xs text-gray-400">
        <p>AI will generate a new scene based on your character, recent decisions, and story context.</p>
        {!process.env.NEXT_PUBLIC_OPENAI_API_KEY && (
          <p className="text-yellow-400 mt-1">
            ⚠️ OpenAI API key not configured. Fallback scenes will be used.
          </p>
        )}
      </div>
    </div>
  );
}
