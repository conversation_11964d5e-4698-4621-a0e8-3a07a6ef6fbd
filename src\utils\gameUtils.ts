import { GameStats, Character, AbilityCheck, CharacterClass } from '../types/game';
import { GAME_CONSTANTS, PROGRESSION_CONFIG } from '../config/gameConfig';

/**
 * Generate a unique ID for game entities
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

/**
 * Create a new character with the given class and name
 */
export function create<PERSON>haracter(
  name: string, 
  characterClass: CharacterClass
): Character {
  return {
    id: generateId(),
    name,
    class: characterClass,
    level: 1,
    currentStats: { ...characterClass.stats },
    abilities: [...characterClass.abilities],
    experience: 0,
    health: GAME_CONSTANTS.STARTING_HEALTH,
    maxHealth: GAME_CONSTANTS.STARTING_HEALTH
  };
}

/**
 * Calculate if an ability check succeeds
 */
export function rollAbilityCheck(
  character: Character, 
  check: AbilityCheck
): { success: boolean; roll: number; total: number } {
  const roll = Math.floor(Math.random() * 20) + 1; // d20
  const statValue = character.currentStats[check.stat];
  const total = roll + statValue;
  
  return {
    success: total >= check.difficulty,
    roll,
    total
  };
}

/**
 * Calculate experience needed for next level
 */
export function getExperienceForLevel(level: number): number {
  return level * GAME_CONSTANTS.LEVEL_UP_EXPERIENCE;
}

/**
 * Check if character can level up
 */
export function canLevelUp(character: Character): boolean {
  if (character.level >= PROGRESSION_CONFIG.maxLevel) return false;
  return character.experience >= getExperienceForLevel(character.level + 1);
}

/**
 * Level up a character
 */
export function levelUpCharacter(character: Character): Character {
  if (!canLevelUp(character)) return character;

  const newLevel = character.level + 1;
  const updatedCharacter = {
    ...character,
    level: newLevel,
    experience: character.experience - getExperienceForLevel(newLevel)
  };

  // Add stat point (player chooses which stat to increase)
  return updatedCharacter;
}

/**
 * Apply damage to character
 */
export function applyDamage(character: Character, damage: number): Character {
  const newHealth = Math.max(0, character.health - damage);
  return {
    ...character,
    health: newHealth
  };
}

/**
 * Heal character
 */
export function healCharacter(character: Character, amount: number): Character {
  const newHealth = Math.min(character.maxHealth, character.health + amount);
  return {
    ...character,
    health: newHealth
  };
}

/**
 * Check if character is alive
 */
export function isCharacterAlive(character: Character): boolean {
  return character.health > 0;
}

/**
 * Calculate total stat value including bonuses
 */
export function getTotalStat(character: Character, stat: keyof GameStats): number {
  return character.currentStats[stat];
}

/**
 * Format currency display
 */
export function formatCurrency(gold: number, tokens: number): string {
  return `${gold}g ${tokens}t`;
}

/**
 * Calculate difficulty class for ability checks
 */
export function getDifficultyClass(difficulty: 'easy' | 'medium' | 'hard' | 'extreme'): number {
  switch (difficulty) {
    case 'easy': return 10;
    case 'medium': return 15;
    case 'hard': return 20;
    case 'extreme': return 25;
    default: return 15;
  }
}

/**
 * Generate random choice from array
 */
export function randomChoice<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Shuffle array using Fisher-Yates algorithm
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Validate character name
 */
export function isValidCharacterName(name: string): boolean {
  return name.trim().length >= 2 && name.trim().length <= 20;
}

/**
 * Calculate health percentage
 */
export function getHealthPercentage(character: Character): number {
  return Math.round((character.health / character.maxHealth) * 100);
}

/**
 * Get character's primary stat
 */
export function getPrimaryStat(character: Character): keyof GameStats {
  const stats = character.currentStats;
  if (stats.might >= stats.wit && stats.might >= stats.charm) return 'might';
  if (stats.wit >= stats.charm) return 'wit';
  return 'charm';
}
