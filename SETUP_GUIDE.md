# TextQuest Setup Guide

This guide will help you set up OpenAI API and Firebase for full TextQuest functionality.

## 🤖 OpenAI API Setup

### 1. Get OpenAI API Key
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-`)

### 2. Configure OpenAI
1. Copy `.env.local.example` to `.env.local`
2. Add your OpenAI API key:
   ```
   NEXT_PUBLIC_OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

### 3. OpenAI Features
With OpenAI configured, you'll get:
- ✅ AI-generated scenes based on player choices
- ✅ Dynamic story progression
- ✅ Context-aware narrative generation
- ✅ Adaptive difficulty and challenges

## 🔥 Firebase Setup

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name (e.g., "textquest-game")
4. Enable Google Analytics (optional)
5. Create project

### 2. Enable Authentication
1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Anonymous" authentication
5. Optionally enable "Email/Password" for persistent accounts

### 3. Create Firestore Database
1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location close to your users
5. Create database

### 4. Get Firebase Configuration
1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Web" icon to add web app
4. Register app with nickname (e.g., "TextQuest Web")
5. Copy the configuration object

### 5. Configure Firebase
Add the Firebase config to your `.env.local`:
```
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### 6. Firebase Features
With Firebase configured, you'll get:
- ✅ User authentication (anonymous & email/password)
- ✅ Cloud save/load functionality
- ✅ Cross-device game progress
- ✅ Player progression tracking
- ✅ Custom campaign storage

## 🚀 Running the Application

### 1. Install Dependencies
```bash
npm install
```

### 2. Set Environment Variables
Create `.env.local` with your API keys (see examples above)

### 3. Start Development Server
```bash
npm run dev
```

### 4. Open Application
Navigate to `http://localhost:3000`

## 🎮 Features Available

### Without API Keys (Local Mode)
- ✅ Character creation and progression
- ✅ Basic gameplay with predefined scenes
- ✅ Local save/load (browser storage)
- ✅ All character classes and abilities
- ✅ Combat and ability systems

### With OpenAI API
- ✅ All local features PLUS:
- ✅ AI-generated scenes and stories
- ✅ Dynamic narrative adaptation
- ✅ Contextual story progression
- ✅ Infinite replayability

### With Firebase
- ✅ All local features PLUS:
- ✅ Cloud save/load across devices
- ✅ User accounts and authentication
- ✅ Persistent player progress
- ✅ Custom campaign sharing

### With Both APIs
- ✅ Full TextQuest experience
- ✅ AI-generated content with cloud saves
- ✅ Cross-device AI campaigns
- ✅ Persistent AI-generated worlds

## 🔧 Troubleshooting

### OpenAI Issues
- **"API key not found"**: Check `.env.local` file exists and key is correct
- **"Rate limit exceeded"**: You've hit OpenAI usage limits, wait or upgrade plan
- **"Model not found"**: Ensure you have access to GPT-4 or change to GPT-3.5

### Firebase Issues
- **"Firebase not initialized"**: Check all Firebase env variables are set
- **"Permission denied"**: Ensure Firestore rules allow authenticated users
- **"Auth domain error"**: Verify Firebase auth domain in config

### General Issues
- **Environment variables not loading**: Restart development server after changing `.env.local`
- **Build errors**: Run `npm install` to ensure all dependencies are installed
- **CORS errors**: Ensure you're running on localhost:3000 or add your domain to Firebase

## 💡 Tips

1. **Start Simple**: Begin with local mode, then add OpenAI, then Firebase
2. **Test Incrementally**: Test each integration separately
3. **Monitor Usage**: Keep an eye on OpenAI API usage and Firebase quotas
4. **Backup Saves**: Export important game saves before major changes
5. **Development vs Production**: Use different Firebase projects for dev/prod

## 📚 Next Steps

Once everything is set up:
1. Create your first character
2. Try the AI scene generator
3. Save your progress to the cloud
4. Experiment with different story directions
5. Share your custom campaigns

Enjoy your TextQuest adventure! 🎲✨
