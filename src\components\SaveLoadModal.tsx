'use client';

import React, { useState, useEffect } from 'react';
import { SaveData } from '../types/game';
import { FirebaseService, SaveSlot } from '../services/firebaseService';
import { useGameStore } from '../store/gameStore';

interface SaveLoadModalProps {
  isOpen: boolean;
  mode: 'save' | 'load';
  onClose: () => void;
  onSave?: (saveId: string) => void;
  onLoad?: (saveData: SaveData) => void;
}

export default function SaveLoadModal({ isOpen, mode, onClose, onSave, onLoad }: SaveLoadModalProps) {
  const [saveSlots, setSaveSlots] = useState<SaveSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saveName, setSaveName] = useState('');
  const [selectedSlot, setSelectedSlot] = useState<SaveSlot | null>(null);

  const gameState = useGameStore();

  useEffect(() => {
    if (isOpen) {
      loadSaveSlots();
    }
  }, [isOpen]);

  const loadSaveSlots = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const slots = await FirebaseService.getSaveSlots();
      setSaveSlots(slots);
    } catch (err) {
      setError('Failed to load save slots');
      console.error('Load save slots error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!saveName.trim()) {
      setError('Please enter a save name');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const saveData: SaveData = {
        campaignId: gameState.currentCampaign?.id || '',
        currentAct: gameState.currentAct,
        currentScene: gameState.currentScene,
        characterState: gameState.players[0], // Assuming single player for now
        synopsisHistory: gameState.synopsisHistory,
        inventory: gameState.inventory,
        decisionsMade: gameState.decisionHistory,
        currency: gameState.currency,
        timestamp: Date.now()
      };

      const saveId = await FirebaseService.saveGame(saveData, saveName);
      onSave?.(saveId);
      onClose();
    } catch (err) {
      setError('Failed to save game');
      console.error('Save game error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleLoad = async () => {
    if (!selectedSlot) {
      setError('Please select a save slot');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const saveData = await FirebaseService.loadGame(selectedSlot.id);
      if (saveData) {
        onLoad?.(saveData);
        onClose();
      } else {
        setError('Failed to load save data');
      }
    } catch (err) {
      setError('Failed to load game');
      console.error('Load game error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (saveId: string) => {
    if (!confirm('Are you sure you want to delete this save?')) return;

    try {
      await FirebaseService.deleteSave(saveId);
      await loadSaveSlots(); // Refresh the list
    } catch (err) {
      setError('Failed to delete save');
      console.error('Delete save error:', err);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">
            {mode === 'save' ? 'Save Game' : 'Load Game'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded text-red-300">
            {error}
          </div>
        )}

        {!FirebaseService.getCurrentUser() && (
          <div className="mb-4 p-3 bg-yellow-900/50 border border-yellow-700 rounded text-yellow-300">
            ⚠️ You need to be signed in to save/load games. Playing as guest with local storage only.
          </div>
        )}

        {mode === 'save' && (
          <div className="mb-6">
            <label className="block text-sm font-semibold text-gray-300 mb-2">
              Save Name:
            </label>
            <input
              type="text"
              value={saveName}
              onChange={(e) => setSaveName(e.target.value)}
              className="w-full p-3 bg-gray-700 border border-gray-600 rounded text-white"
              placeholder="Enter save name..."
              maxLength={50}
            />
          </div>
        )}

        {/* Save Slots */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-white mb-3">
            {mode === 'save' ? 'Existing Saves' : 'Select Save to Load'}
          </h3>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-gray-300">Loading saves...</p>
            </div>
          ) : saveSlots.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              No saved games found
            </div>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {saveSlots.map((slot) => (
                <div
                  key={slot.id}
                  className={`p-3 border rounded cursor-pointer transition-colors ${
                    selectedSlot?.id === slot.id
                      ? 'border-blue-500 bg-blue-900/30'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}
                  onClick={() => mode === 'load' && setSelectedSlot(slot)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-semibold text-white">{slot.name}</h4>
                      <div className="text-sm text-gray-300 mt-1">
                        <div>Character: {slot.saveData.characterState.name} (Level {slot.saveData.characterState.level})</div>
                        <div>Act {slot.saveData.currentAct}, Scene {slot.saveData.currentScene}</div>
                        <div>Saved: {formatDate(slot.updatedAt)}</div>
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(slot.id);
                      }}
                      className="ml-2 px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs text-white transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded font-semibold text-white transition-colors"
          >
            Cancel
          </button>
          
          {mode === 'save' ? (
            <button
              onClick={handleSave}
              disabled={loading || !saveName.trim()}
              className={`px-6 py-2 rounded font-semibold transition-colors ${
                loading || !saveName.trim()
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {loading ? 'Saving...' : 'Save Game'}
            </button>
          ) : (
            <button
              onClick={handleLoad}
              disabled={loading || !selectedSlot}
              className={`px-6 py-2 rounded font-semibold transition-colors ${
                loading || !selectedSlot
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {loading ? 'Loading...' : 'Load Game'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
