'use client';

import React, { useState } from 'react';
import { Character } from '../types/game';
import { ProgressionSystem } from '../systems/progressionSystem';
import { AbilitySystem, Ability } from '../systems/abilitySystem';
import { getHealthPercentage } from '../utils/gameUtils';

interface CharacterSheetProps {
  character: Character;
  isOpen: boolean;
  onClose: () => void;
  onUseAbility?: (ability: Ability) => void;
}

export default function CharacterSheet({ character, isOpen, onClose, onUseAbility }: CharacterSheetProps) {
  const [activeTab, setActiveTab] = useState<'stats' | 'abilities' | 'progression'>('stats');

  if (!isOpen) return null;

  const progressInfo = ProgressionSystem.getProgressToNextLevel(character);
  const characterAbilities = AbilitySystem.getCharacterAbilities(character);
  const powerLevel = ProgressionSystem.getCharacterPowerLevel(character);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-3xl font-bold text-white">{character.name}</h2>
            <p className="text-xl text-gray-300">
              Level {character.level} {character.class.name}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex mb-6 border-b border-gray-700">
          {[
            { id: 'stats', label: 'Stats & Health' },
            { id: 'abilities', label: 'Abilities' },
            { id: 'progression', label: 'Progression' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-2 font-semibold transition-colors ${
                activeTab === tab.id
                  ? 'text-blue-400 border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Stats Tab */}
        {activeTab === 'stats' && (
          <div className="space-y-6">
            {/* Health */}
            <div className="p-4 bg-gray-700 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-3">Health</h3>
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-300">HP</span>
                    <span className="text-white">{character.health}/{character.maxHealth}</span>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-3">
                    <div 
                      className="bg-red-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${getHealthPercentage(character)}%` }}
                    />
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-white">
                    {getHealthPercentage(character)}%
                  </div>
                </div>
              </div>
            </div>

            {/* Core Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="p-4 bg-red-900/30 border border-red-700 rounded-lg text-center">
                <div className="text-3xl font-bold text-red-400 mb-1">
                  {character.currentStats.might}
                </div>
                <div className="text-red-300 font-semibold">MIGHT</div>
                <div className="text-xs text-red-200 mt-1">
                  Physical power & combat
                </div>
              </div>
              <div className="p-4 bg-blue-900/30 border border-blue-700 rounded-lg text-center">
                <div className="text-3xl font-bold text-blue-400 mb-1">
                  {character.currentStats.wit}
                </div>
                <div className="text-blue-300 font-semibold">WIT</div>
                <div className="text-xs text-blue-200 mt-1">
                  Intelligence & magic
                </div>
              </div>
              <div className="p-4 bg-green-900/30 border border-green-700 rounded-lg text-center">
                <div className="text-3xl font-bold text-green-400 mb-1">
                  {character.currentStats.charm}
                </div>
                <div className="text-green-300 font-semibold">CHARM</div>
                <div className="text-xs text-green-200 mt-1">
                  Social skills & healing
                </div>
              </div>
            </div>

            {/* Character Info */}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-700 rounded-lg">
                <h4 className="text-lg font-semibold text-white mb-2">Character Info</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Class:</span>
                    <span className="text-white">{character.class.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Level:</span>
                    <span className="text-white">{character.level}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Experience:</span>
                    <span className="text-white">{character.experience} XP</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Power Level:</span>
                    <span className="text-yellow-400">{powerLevel}</span>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-700 rounded-lg">
                <h4 className="text-lg font-semibold text-white mb-2">Class Bonuses</h4>
                <div className="space-y-1 text-sm text-gray-300">
                  <div>Base Stats: {character.class.stats.might}/{character.class.stats.wit}/{character.class.stats.charm}</div>
                  <div>Starting Abilities: {character.class.abilities.length}</div>
                  <div>Total Abilities: {character.abilities.length}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Abilities Tab */}
        {activeTab === 'abilities' && (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-white">Abilities & Skills</h3>
            
            {characterAbilities.length > 0 ? (
              <div className="grid gap-4">
                {characterAbilities.map((ability, index) => (
                  <div key={index} className="p-4 bg-gray-700 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="text-lg font-semibold text-white">{ability.name}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-300">
                          {ability.currentUses}/{ability.usesPerAct} uses
                        </span>
                        {onUseAbility && AbilitySystem.canUseAbility(ability) && (
                          <button
                            onClick={() => onUseAbility(ability)}
                            className="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded text-sm font-semibold transition-colors"
                          >
                            Use
                          </button>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-gray-300 text-sm mb-3">{ability.description}</p>
                    
                    <div className="flex items-center gap-4 text-xs">
                      <span className={`px-2 py-1 rounded ${
                        ability.effect.type === 'damage' ? 'bg-red-700 text-red-200' :
                        ability.effect.type === 'heal' ? 'bg-green-700 text-green-200' :
                        ability.effect.type === 'buff' ? 'bg-blue-700 text-blue-200' :
                        'bg-purple-700 text-purple-200'
                      }`}>
                        {ability.effect.type.toUpperCase()}
                      </span>
                      
                      {ability.effect.value && (
                        <span className="text-gray-300">
                          {ability.effect.type === 'damage' ? 'Damage' : 
                           ability.effect.type === 'heal' ? 'Healing' : 'Value'}: {ability.effect.value}
                        </span>
                      )}
                      
                      {ability.cooldown > 0 && (
                        <span className="text-gray-300">Cooldown: {ability.cooldown}</span>
                      )}
                    </div>
                    
                    {ability.effect.specialEffect && (
                      <div className="mt-2 text-xs text-yellow-300">
                        Special: {ability.effect.specialEffect}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                No abilities available
              </div>
            )}
          </div>
        )}

        {/* Progression Tab */}
        {activeTab === 'progression' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Character Progression</h3>
            
            {/* Experience Progress */}
            <div className="p-4 bg-gray-700 rounded-lg">
              <h4 className="text-lg font-semibold text-white mb-3">Experience Progress</h4>
              {character.level < 5 ? (
                <>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-300">Progress to Level {character.level + 1}</span>
                    <span className="text-white">{progressInfo.current}/{progressInfo.required} XP</span>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-3 mb-2">
                    <div 
                      className="bg-yellow-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${progressInfo.percentage}%` }}
                    />
                  </div>
                  <div className="text-center text-yellow-400 font-semibold">
                    {progressInfo.percentage}% Complete
                  </div>
                </>
              ) : (
                <div className="text-center text-yellow-400 font-semibold">
                  Maximum Level Reached!
                </div>
              )}
            </div>

            {/* Level History */}
            <div className="p-4 bg-gray-700 rounded-lg">
              <h4 className="text-lg font-semibold text-white mb-3">Level History</h4>
              <div className="space-y-2">
                {Array.from({ length: character.level }, (_, i) => i + 1).map((level) => (
                  <div key={level} className="flex items-center gap-3 text-sm">
                    <div className="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold">
                      {level}
                    </div>
                    <div className="text-gray-300">
                      Level {level} - {level === 1 ? 'Character Created' : 'Advanced'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
