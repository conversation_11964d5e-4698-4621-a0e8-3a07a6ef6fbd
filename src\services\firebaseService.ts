import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInAnonymously, 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  User
} from 'firebase/auth';
import { 
  getFirestore, 
  doc, 
  setDoc, 
  getDoc, 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  deleteDoc
} from 'firebase/firestore';
import { SaveData, PlayerProgress, Campaign } from '../types/game';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

export interface SaveSlot {
  id: string;
  name: string;
  saveData: SaveData;
  createdAt: number;
  updatedAt: number;
}

export class FirebaseService {
  // Authentication
  static async signInAnonymously(): Promise<User> {
    const result = await signInAnonymously(auth);
    return result.user;
  }

  static async signInWithEmail(email: string, password: string): Promise<User> {
    const result = await signInWithEmailAndPassword(auth, email, password);
    return result.user;
  }

  static async createAccount(email: string, password: string): Promise<User> {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    return result.user;
  }

  static async signOut(): Promise<void> {
    await signOut(auth);
  }

  static getCurrentUser(): User | null {
    return auth.currentUser;
  }

  // Save Game Data
  static async saveGame(saveData: SaveData, slotName: string = 'Auto Save'): Promise<string> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('User must be authenticated to save game');
    }

    const saveId = `save_${Date.now()}`;
    const saveSlot: SaveSlot = {
      id: saveId,
      name: slotName,
      saveData,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const saveRef = doc(db, 'saves', user.uid, 'slots', saveId);
    await setDoc(saveRef, saveSlot);

    return saveId;
  }

  static async loadGame(saveId: string): Promise<SaveData | null> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('User must be authenticated to load game');
    }

    const saveRef = doc(db, 'saves', user.uid, 'slots', saveId);
    const saveDoc = await getDoc(saveRef);

    if (saveDoc.exists()) {
      const saveSlot = saveDoc.data() as SaveSlot;
      return saveSlot.saveData;
    }

    return null;
  }

  static async getSaveSlots(): Promise<SaveSlot[]> {
    const user = this.getCurrentUser();
    if (!user) {
      return [];
    }

    const savesRef = collection(db, 'saves', user.uid, 'slots');
    const q = query(savesRef, orderBy('updatedAt', 'desc'), limit(10));
    const querySnapshot = await getDocs(q);

    const saves: SaveSlot[] = [];
    querySnapshot.forEach((doc) => {
      saves.push(doc.data() as SaveSlot);
    });

    return saves;
  }

  static async deleteSave(saveId: string): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('User must be authenticated to delete save');
    }

    const saveRef = doc(db, 'saves', user.uid, 'slots', saveId);
    await deleteDoc(saveRef);
  }

  // Player Progress
  static async saveProgress(progress: PlayerProgress): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('User must be authenticated to save progress');
    }

    const progressRef = doc(db, 'progress', user.uid);
    await setDoc(progressRef, {
      ...progress,
      updatedAt: Date.now()
    }, { merge: true });
  }

  static async loadProgress(): Promise<PlayerProgress | null> {
    const user = this.getCurrentUser();
    if (!user) {
      return null;
    }

    const progressRef = doc(db, 'progress', user.uid);
    const progressDoc = await getDoc(progressRef);

    if (progressDoc.exists()) {
      const data = progressDoc.data();
      return {
        completedCampaigns: data.completedCampaigns || [],
        unlockedClasses: data.unlockedClasses || ['Warrior', 'Mage', 'Rogue', 'Healer'],
        unlockedCampaigns: data.unlockedCampaigns || [],
        unlockedItems: data.unlockedItems || [],
        currency: data.currency || { gold: 0, tokens: 0 },
        achievements: data.achievements || []
      };
    }

    return null;
  }

  // Campaign Storage (for custom/generated campaigns)
  static async saveCampaign(campaign: Campaign): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('User must be authenticated to save campaign');
    }

    const campaignRef = doc(db, 'campaigns', user.uid, 'custom', campaign.id);
    await setDoc(campaignRef, {
      ...campaign,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });
  }

  static async loadCampaign(campaignId: string): Promise<Campaign | null> {
    const user = this.getCurrentUser();
    if (!user) {
      return null;
    }

    const campaignRef = doc(db, 'campaigns', user.uid, 'custom', campaignId);
    const campaignDoc = await getDoc(campaignRef);

    if (campaignDoc.exists()) {
      return campaignDoc.data() as Campaign;
    }

    return null;
  }

  static async getUserCampaigns(): Promise<Campaign[]> {
    const user = this.getCurrentUser();
    if (!user) {
      return [];
    }

    const campaignsRef = collection(db, 'campaigns', user.uid, 'custom');
    const q = query(campaignsRef, orderBy('updatedAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const campaigns: Campaign[] = [];
    querySnapshot.forEach((doc) => {
      campaigns.push(doc.data() as Campaign);
    });

    return campaigns;
  }

  // Utility functions
  static async initializeUser(): Promise<void> {
    let user = this.getCurrentUser();
    
    if (!user) {
      user = await this.signInAnonymously();
    }

    // Check if user has existing progress, if not create default
    const existingProgress = await this.loadProgress();
    if (!existingProgress) {
      const defaultProgress: PlayerProgress = {
        completedCampaigns: [],
        unlockedClasses: ['Warrior', 'Mage', 'Rogue', 'Healer'],
        unlockedCampaigns: [],
        unlockedItems: [],
        currency: { gold: 50, tokens: 0 },
        achievements: []
      };
      await this.saveProgress(defaultProgress);
    }
  }

  static async quickSave(saveData: SaveData): Promise<string> {
    return this.saveGame(saveData, 'Quick Save');
  }

  static async autoSave(saveData: SaveData): Promise<string> {
    return this.saveGame(saveData, 'Auto Save');
  }

  // Error handling wrapper
  static async withErrorHandling<T>(
    operation: () => Promise<T>,
    fallback?: T
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      console.error('Firebase operation failed:', error);
      return fallback || null;
    }
  }
}

// Auth state observer
export const onAuthStateChanged = (callback: (user: User | null) => void) => {
  return auth.onAuthStateChanged(callback);
};
