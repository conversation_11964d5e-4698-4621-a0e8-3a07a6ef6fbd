import { Character, GameStats } from '../types/game';
import { PROGRESSION_CONFIG } from '../config/gameConfig';

export interface LevelUpOption {
  type: 'stat' | 'ability' | 'passive';
  name: string;
  description: string;
  statIncrease?: Partial<GameStats>;
  newAbility?: string;
  passiveEffect?: string;
}

export interface LevelUpReward {
  level: number;
  options: LevelUpOption[];
  healthIncrease: number;
}

export class ProgressionSystem {
  static getExperienceForLevel(level: number): number {
    return level * 100; // 100 XP per level
  }

  static canLevelUp(character: Character): boolean {
    if (character.level >= PROGRESSION_CONFIG.maxLevel) return false;
    const requiredXP = this.getExperienceForLevel(character.level + 1);
    return character.experience >= requiredXP;
  }

  static getLevelUpOptions(character: Character): LevelUpOption[] {
    const level = character.level + 1;
    const options: LevelUpOption[] = [];

    // Always offer stat increases
    const availableStats: (keyof GameStats)[] = ['might', 'wit', 'charm'];
    availableStats.forEach(stat => {
      if (character.currentStats[stat] < PROGRESSION_CONFIG.maxStatValue) {
        options.push({
          type: 'stat',
          name: `Increase ${stat.charAt(0).toUpperCase() + stat.slice(1)}`,
          description: `Gain +1 ${stat.charAt(0).toUpperCase() + stat.slice(1)} (current: ${character.currentStats[stat]})`,
          statIncrease: { [stat]: 1 } as Partial<GameStats>
        });
      }
    });

    // Class-specific abilities and passives based on level
    const classOptions = this.getClassSpecificOptions(character, level);
    options.push(...classOptions);

    return options;
  }

  private static getClassSpecificOptions(character: Character, level: number): LevelUpOption[] {
    const className = character.class.name;
    const options: LevelUpOption[] = [];

    switch (className) {
      case 'Warrior':
        if (level === 2) {
          options.push({
            type: 'passive',
            name: 'Second Wind',
            description: 'Automatically revive once per campaign when health reaches 0',
            passiveEffect: 'second-wind'
          });
        }
        if (level === 3) {
          options.push({
            type: 'ability',
            name: 'Intimidating Shout',
            description: 'New ability: Force enemies to flee or cower in fear',
            newAbility: 'Intimidating Shout'
          });
        }
        if (level === 4) {
          options.push({
            type: 'passive',
            name: 'Armor Mastery',
            description: 'Reduce all incoming damage by 2 points',
            passiveEffect: 'armor-mastery'
          });
        }
        break;

      case 'Mage':
        if (level === 2) {
          options.push({
            type: 'ability',
            name: 'Magic Missile',
            description: 'New ability: Guaranteed hit magical projectile',
            newAbility: 'Magic Missile'
          });
        }
        if (level === 3) {
          options.push({
            type: 'passive',
            name: 'Spell Focus',
            description: 'Reroll 1 failed Wit check per act',
            passiveEffect: 'spell-focus'
          });
        }
        if (level === 4) {
          options.push({
            type: 'ability',
            name: 'Teleport',
            description: 'New ability: Instantly move to safety or advantageous position',
            newAbility: 'Teleport'
          });
        }
        break;

      case 'Rogue':
        if (level === 2) {
          options.push({
            type: 'passive',
            name: 'Lucky',
            description: 'Reroll 1 failed ability check per act',
            passiveEffect: 'lucky'
          });
        }
        if (level === 3) {
          options.push({
            type: 'ability',
            name: 'Poison Blade',
            description: 'New ability: Coat weapon with poison for extra damage over time',
            newAbility: 'Poison Blade'
          });
        }
        if (level === 4) {
          options.push({
            type: 'passive',
            name: 'Evasion',
            description: 'Automatically avoid the first attack each encounter',
            passiveEffect: 'evasion'
          });
        }
        break;

      case 'Healer':
        if (level === 2) {
          options.push({
            type: 'ability',
            name: 'Group Heal',
            description: 'New ability: Heal all party members at once',
            newAbility: 'Group Heal'
          });
        }
        if (level === 3) {
          options.push({
            type: 'passive',
            name: 'Divine Favor',
            description: 'Start each act with a blessing that provides +1 to all stats',
            passiveEffect: 'divine-favor'
          });
        }
        if (level === 4) {
          options.push({
            type: 'ability',
            name: 'Resurrection',
            description: 'New ability: Bring back a fallen ally once per campaign',
            newAbility: 'Resurrection'
          });
        }
        break;
    }

    return options;
  }

  static applyLevelUp(character: Character, selectedOption: LevelUpOption): Character {
    const newLevel = character.level + 1;
    const requiredXP = this.getExperienceForLevel(newLevel);
    
    let updatedCharacter = {
      ...character,
      level: newLevel,
      experience: character.experience - requiredXP,
      maxHealth: character.maxHealth + 20, // +20 HP per level
      health: character.health + 20 // Also heal when leveling up
    };

    // Apply the selected option
    switch (selectedOption.type) {
      case 'stat':
        if (selectedOption.statIncrease) {
          updatedCharacter.currentStats = {
            ...updatedCharacter.currentStats,
            might: updatedCharacter.currentStats.might + (selectedOption.statIncrease.might || 0),
            wit: updatedCharacter.currentStats.wit + (selectedOption.statIncrease.wit || 0),
            charm: updatedCharacter.currentStats.charm + (selectedOption.statIncrease.charm || 0)
          };
        }
        break;

      case 'ability':
        if (selectedOption.newAbility) {
          updatedCharacter.abilities = [...updatedCharacter.abilities, selectedOption.newAbility];
        }
        break;

      case 'passive':
        // Passive effects would be tracked separately in a full implementation
        // For now, we'll just note them in the character's abilities list
        if (selectedOption.passiveEffect) {
          updatedCharacter.abilities = [...updatedCharacter.abilities, selectedOption.name];
        }
        break;
    }

    return updatedCharacter;
  }

  static grantExperience(character: Character, amount: number): Character {
    return {
      ...character,
      experience: character.experience + amount
    };
  }

  static getProgressToNextLevel(character: Character): { current: number; required: number; percentage: number } {
    if (character.level >= PROGRESSION_CONFIG.maxLevel) {
      return { current: 0, required: 0, percentage: 100 };
    }

    const required = this.getExperienceForLevel(character.level + 1);
    const current = character.experience;
    const percentage = Math.min(100, Math.round((current / required) * 100));

    return { current, required, percentage };
  }

  static getCharacterPowerLevel(character: Character): number {
    // Calculate overall character power for balancing purposes
    const statTotal = character.currentStats.might + character.currentStats.wit + character.currentStats.charm;
    const levelBonus = character.level * 5;
    const abilityBonus = character.abilities.length * 2;
    
    return statTotal + levelBonus + abilityBonus;
  }

  static suggestExperienceReward(difficulty: 'easy' | 'medium' | 'hard' | 'extreme'): number {
    switch (difficulty) {
      case 'easy': return 10;
      case 'medium': return 25;
      case 'hard': return 50;
      case 'extreme': return 100;
      default: return 25;
    }
  }
}
